plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}
android {

    namespace "cn.cleartv.opus"
    compileSdk = libs.versions.sdk.compile.get().toInteger()

    defaultConfig {
        minSdk = libs.versions.sdk.min.get().toInteger()
        targetSdk = libs.versions.sdk.target.get().toInteger()

        externalNativeBuild {
            cmake {
                cppFlags ""
            }

            ndk {
                abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.18.1"
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

kotlin {
    jvmToolchain(11)
}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation libs.androidx.core.ktx
}
