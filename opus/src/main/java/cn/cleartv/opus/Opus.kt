package cn.cleartv.opus

import android.util.Log

class Opus {

    companion object {

        const val TAG = "CodecOpus"

        const val OPUS_APPLICATION_VOIP = 2048
        const val OPUS_APPLICATION_AUDIO = 2049
        const val OPUS_APPLICATION_RESTRICTED_LOWDELAY = 2051

        init {
            try { System.loadLibrary("easyopus") }
            catch (e: Exception) { Log.e(TAG, "Couldn't load opus library: $e") }
        }
    }

    //
    // Encoder
    //

    external fun encoderInit(sampleRate: Int, numChannels: Int, application: Int): Int
    external fun encoderSetBitrate(bitrate: Int): Int
    external fun encoderSetComplexity(complexity: Int): Int
    external fun encode(bytes: ByteArray, frameSize: Int): ByteArray?
    external fun encode(shorts: ShortArray, frameSize: Int): ShortArray?
    external fun encoderRelease()

    //
    // Decoder
    //

    external fun decoderInit(sampleRate: Int, numChannels: Int): Int
    external fun decode(bytes: ByteArray, frameSize: Int): ByteArray?
    external fun decode(shorts: ShortArray, frameSize: Int): ShortArray?
    external fun decoderRelease()

    //
    // Utils
    //

    external fun convert(bytes: ByteArray): ShortArray?
    external fun convert(shorts: ShortArray): ByteArray?
}
