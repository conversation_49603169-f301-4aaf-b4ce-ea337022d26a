<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终端管理系统 - 登录</title>
    <link rel="stylesheet" href="common.css">
    <link rel="stylesheet" href="components.css">
    <style>
        /* 登录页面特定样式 */
        .login-page {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            min-height: 100vh;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
        }

        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .login-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .login-logo {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }
    </style>
</head>
<body class="login-page flex items-center justify-center">
<div class="login-container p-lg">
    <div class="card login-card shadow-xl">
        <div class="card-body">
            <div class="login-header">
                <div class="login-logo">🖥️</div>
                <h1 class="text-xxl font-bold text-primary m-0">终端管理系统</h1>
                <p class="text-muted mt-sm">请登录您的账户</p>
            </div>

            <form id="loginForm">
                <div class="form-group">
                    <label class="form-label" for="username">用户名</label>
                    <input type="text" class="form-control" id="username" name="username" required
                           placeholder="请输入用户名">
                </div>
                <div class="form-group">
                    <label class="form-label" for="password">密码</label>
                    <input type="password" class="form-control" id="password" name="password" required
                           placeholder="请输入密码">
                </div>
                <button type="submit" class="btn btn-primary w-full">
                    <span>🔐</span>
                    <span>登录</span>
                </button>
            </form>
            <div id="errorMessage"></div>
        </div>
    </div>
</div>

<!-- 在 </body> 标签前添加 -->
<script src="api.js"></script>
<script src="utils.js"></script>
<script>
    // 全局错误处理
    window.addEventListener('error', function(e) {
        console.error('Global error:', e.error);
        console.error('Error message:', e.message);
        console.error('Error filename:', e.filename);
        console.error('Error line:', e.lineno);
    });

    console.log('Login page scripts loaded'); // 调试信息
    console.log('apiClient:', typeof apiClient); // 检查apiClient是否加载
    console.log('showLoading:', typeof showLoading); // 检查utils是否加载

    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, setting up login form listener');
        const loginForm = document.getElementById('loginForm');
        if (!loginForm) {
            console.error('Login form not found!');
            return;
        }

        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            console.log('Login form submitted'); // 调试信息

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');
            const submitBtn = e.target.querySelector('button[type="submit"]');

            console.log('Username:', username, 'Password length:', password.length); // 调试信息

            // 清除之前的错误信息
            errorMessage.innerHTML = '';

            // 检查输入
            if (!username || !password) {
                errorMessage.innerHTML = '<div class="alert alert-danger mt-md">请输入用户名和密码</div>';
                return;
            }

            try {
                console.log('Starting login request...'); // 调试信息

                // 直接设置按钮状态，不依赖utils.js
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner spinner-sm"></span> 登录中...';

                const data = await apiClient.login(username, password);
                console.log('Login response:', data); // 调试信息

                // 检查响应数据结构
                if (data && data.data && data.data.id) {
                    // 登录成功，保存session id并跳转到主页
                    localStorage.setItem('sessionId', data.data.id);
                    showMessage('登录成功，正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = 'home.html';
                    }, 1000);
                } else if (data && data.sessionId) {
                    // 兼容不同的响应格式
                    localStorage.setItem('sessionId', data.sessionId);
                    showMessage('登录成功，正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = 'home.html';
                    }, 1000);
                } else {
                    console.error('Unexpected response format:', data);
                    errorMessage.innerHTML = '<div class="alert alert-danger mt-md">登录响应格式错误</div>';
                }
            } catch (error) {
                console.error('Login error:', error); // 调试信息
                errorMessage.innerHTML = `<div class="alert alert-danger mt-md">登录失败: ${error.message}</div>`;
            } finally {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<span>🔐</span><span>登录</span>';
            }
        });

        // 测试按钮事件
        const testBtn = document.getElementById('testBtn');
        if (testBtn) {
            testBtn.addEventListener('click', async function() {
                console.log('Test button clicked');

                // 测试基本功能
                const username = document.getElementById('username').value || 'test';
                const password = document.getElementById('password').value || 'test';

                console.log('Testing with username:', username);

                try {
                    // 直接测试登录API
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                    });

                    console.log('Test response status:', response.status);
                    const responseText = await response.text();
                    console.log('Test response text:', responseText);

                    alert(`测试完成！状态: ${response.status}\n响应: ${responseText.substring(0, 100)}...`);
                } catch (error) {
                    console.error('Test error:', error);
                    alert('测试失败: ' + error.message);
                }
            });
        }
    });
</script>

</body>
</html>
