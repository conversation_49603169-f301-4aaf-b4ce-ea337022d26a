/* ==========================================================================
   通用样式系统 - 统一的设计语言
   ========================================================================== */

/* CSS变量定义 - 统一的设计令牌 */
:root {
    /* 颜色系统 */
    --primary-color: #007bff;
    --primary-hover: #0069d9;
    --primary-active: #0056b3;
    --secondary-color: #6c757d;
    --secondary-hover: #5a6268;
    --success-color: #28a745;
    --success-hover: #218838;
    --danger-color: #dc3545;
    --danger-hover: #c82333;
    --warning-color: #ffc107;
    --warning-hover: #e0a800;
    --info-color: #17a2b8;
    --info-hover: #138496;
    
    /* 中性色 - 改善对比度 */
    --white: #ffffff;
    --gray-50: #f8f9fa;
    --gray-100: #e9ecef;
    --gray-200: #dee2e6;
    --gray-300: #ced4da;
    --gray-400: #adb5bd;
    --gray-500: #6c757d;
    --gray-600: #495057;
    --gray-700: #343a40;
    --gray-800: #212529;
    --gray-900: #000000;
    
    /* 语义化颜色 - 改善对比度 */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-700);
    --text-muted: var(--gray-600);
    --bg-primary: var(--white);
    --bg-secondary: var(--gray-50);
    --bg-dark: var(--gray-700);
    --border-color: var(--gray-300);
    --border-light: var(--gray-200);
    
    /* 间距系统 */
    --spacing-xs: 0.25rem;   /* 4px */
    --spacing-sm: 0.5rem;    /* 8px */
    --spacing-md: 1rem;      /* 16px */
    --spacing-lg: 1.5rem;    /* 24px */
    --spacing-xl: 2rem;      /* 32px */
    --spacing-xxl: 3rem;     /* 48px */
    
    /* 字体系统 */
    --font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-family-mono: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --font-size-xs: 0.75rem;   /* 12px */
    --font-size-sm: 0.875rem;  /* 14px */
    --font-size-base: 1rem;    /* 16px */
    --font-size-lg: 1.125rem;  /* 18px */
    --font-size-xl: 1.25rem;   /* 20px */
    --font-size-xxl: 1.5rem;   /* 24px */
    --font-size-xxxl: 2rem;    /* 32px */
    
    /* 行高 */
    --line-height-tight: 1.25;
    --line-height-base: 1.5;
    --line-height-relaxed: 1.75;
    
    /* 圆角 */
    --border-radius-sm: 0.25rem;
    --border-radius-base: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --border-radius-full: 9999px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 过渡动画 */
    --transition-fast: 150ms ease;
    --transition-base: 250ms ease;
    --transition-slow: 350ms ease;
    
    /* Z-index层级 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ==========================================================================
   基础重置和全局样式
   ========================================================================== */

/* 现代CSS重置 */
*, *::before, *::after {
    box-sizing: border-box;
}

html {
    line-height: var(--line-height-base);
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
}

body {
    margin: 0;
    font-family: var(--font-family-base);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 移除默认样式 */
h1, h2, h3, h4, h5, h6, p, ul, ol, li, figure, figcaption, blockquote, dl, dd {
    margin: 0;
    padding: 0;
}

ul, ol {
    list-style: none;
}

img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    max-width: 100%;
}

img, video {
    height: auto;
}

input, button, textarea, select {
    font: inherit;
}

/* ==========================================================================
   布局系统
   ========================================================================== */

/* Flexbox工具类 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-grow { flex-grow: 1; }

/* 对齐 */
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

/* 间距工具类 */
.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

/* 内边距 */
.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.px-xs { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }

.py-xs { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-sm { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-md { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }

/* 外边距 */
.m-0 { margin: 0; }
.m-auto { margin: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

/* 宽度和高度 */
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }
.h-screen { height: 100vh; }

/* 显示控制 */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* 溢出控制 */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* 位置 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* ==========================================================================
   文本系统
   ========================================================================== */

/* 字体大小 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-xxl { font-size: var(--font-size-xxl); }
.text-xxxl { font-size: var(--font-size-xxxl); }

/* 字体粗细 */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* 文本颜色 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-white { color: var(--white); }
.text-success { color: var(--success-color); }
.text-danger { color: var(--danger-color); }
.text-warning { color: var(--warning-color); }
.text-info { color: var(--info-color); }

/* 行高 */
.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-base); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

/* ==========================================================================
   背景和边框
   ========================================================================== */

/* 背景颜色 */
.bg-white { background-color: var(--white); }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }
.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-info { background-color: var(--info-color); }

/* 边框 */
.border { border: 1px solid var(--border-color); }
.border-0 { border: 0; }
.border-t { border-top: 1px solid var(--border-color); }
.border-b { border-bottom: 1px solid var(--border-color); }
.border-l { border-left: 1px solid var(--border-color); }
.border-r { border-right: 1px solid var(--border-color); }

/* 圆角 */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded { border-radius: var(--border-radius-base); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-full { border-radius: var(--border-radius-full); }

/* 阴影 */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-base); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* ==========================================================================
   过渡动画
   ========================================================================== */

.transition { transition: all var(--transition-base); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }
.transition-colors { transition: color var(--transition-base), background-color var(--transition-base), border-color var(--transition-base); }
.transition-transform { transition: transform var(--transition-base); }
.transition-opacity { transition: opacity var(--transition-base); }

/* 变换 */
.transform { transform: translateZ(0); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }

/* 透明度 */
.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* ==========================================================================
   交互状态
   ========================================================================== */

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-default { cursor: default; }

.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

/* 焦点样式 */
.focus-outline {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.focus-outline:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 悬停效果 */
.hover-scale:hover {
    transform: scale(1.05);
}

.hover-shadow:hover {
    box-shadow: var(--shadow-lg);
}

/* ==========================================================================
   移动端和可访问性增强
   ========================================================================== */

/* 改善触摸目标大小 */
@media (hover: none) and (pointer: coarse) {
    .btn, .form-control, [role="button"], [tabindex] {
        min-height: 44px;
        min-width: 44px;
    }
}

/* 改善焦点可见性 */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 跳过链接 - 可访问性 */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--border-radius-base);
    z-index: var(--z-tooltip);
}

.skip-link:focus {
    top: 6px;
}

/* 屏幕阅读器专用文本 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --text-primary: var(--gray-900);
        --text-secondary: var(--gray-800);
        --text-muted: var(--gray-700);
        --border-color: var(--gray-400);
        --border-light: var(--gray-300);
    }

    .btn {
        border-width: 2px;
    }

    .card {
        border-width: 2px;
    }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
    :root {
        --spacing-xs: 0.125rem;   /* 2px */
        --spacing-sm: 0.25rem;    /* 4px */
        --spacing-md: 0.5rem;     /* 8px */
        --spacing-lg: 0.75rem;    /* 12px */
        --spacing-xl: 1rem;       /* 16px */
        --spacing-xxl: 1.5rem;    /* 24px */
    }

    body {
        font-size: var(--font-size-sm);
    }

    .text-lg {
        font-size: var(--font-size-base) !important;
    }

    .text-xl {
        font-size: var(--font-size-lg) !important;
    }

    .text-xxl {
        font-size: var(--font-size-xl) !important;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
    .min-h-screen {
        min-height: auto;
    }

    .h-screen {
        height: auto;
    }
}
