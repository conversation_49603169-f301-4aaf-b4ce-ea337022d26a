/**
 * 通用工具库 - 提供常用的UI交互和工具函数
 */

// ==========================================================================
// 模态框管理器
// ==========================================================================

class ModalManager {
    constructor() {
        this.activeModals = new Set();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModals.size > 0) {
                const lastModal = Array.from(this.activeModals).pop();
                this.hide(lastModal);
            }
        });

        // 点击背景关闭模态框
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal') && this.activeModals.has(e.target)) {
                this.hide(e.target);
            }
        });
    }

    show(modalElement) {
        if (!modalElement) return;
        
        modalElement.style.display = 'flex';
        // 强制重绘
        modalElement.offsetHeight;
        modalElement.classList.add('show');
        
        this.activeModals.add(modalElement);
        document.body.style.overflow = 'hidden';
    }

    hide(modalElement) {
        if (!modalElement) return;
        
        modalElement.classList.remove('show');
        this.activeModals.delete(modalElement);
        
        setTimeout(() => {
            modalElement.style.display = 'none';
            if (this.activeModals.size === 0) {
                document.body.style.overflow = '';
            }
        }, 250);
    }

    hideAll() {
        this.activeModals.forEach(modal => this.hide(modal));
    }
}

// ==========================================================================
// 消息提示管理器
// ==========================================================================

class MessageManager {
    constructor() {
        this.container = null;
        this.createContainer();
    }

    createContainer() {
        this.container = document.createElement('div');
        this.container.className = 'message-container';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            pointer-events: none;
        `;
        document.body.appendChild(this.container);
    }

    show(message, type = 'info', duration = 4000) {
        const messageEl = document.createElement('div');
        messageEl.className = `alert alert-${type}`;
        messageEl.style.cssText = `
            margin-bottom: 10px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            pointer-events: auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        `;
        messageEl.textContent = message;

        this.container.appendChild(messageEl);

        // 触发动画
        requestAnimationFrame(() => {
            messageEl.style.opacity = '1';
            messageEl.style.transform = 'translateX(0)';
        });

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                this.hide(messageEl);
            }, duration);
        }

        return messageEl;
    }

    hide(messageEl) {
        if (!messageEl || !messageEl.parentNode) return;
        
        messageEl.style.opacity = '0';
        messageEl.style.transform = 'translateX(100%)';
        
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }

    success(message, duration) {
        return this.show(message, 'success', duration);
    }

    error(message, duration) {
        return this.show(message, 'danger', duration);
    }

    warning(message, duration) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration) {
        return this.show(message, 'info', duration);
    }
}

// ==========================================================================
// 加载状态管理器
// ==========================================================================

class LoadingManager {
    constructor() {
        this.loadingElements = new Map();
    }

    show(element, text = '加载中...') {
        if (!element) return;

        const originalContent = element.innerHTML;
        const originalDisabled = element.disabled;
        
        this.loadingElements.set(element, {
            originalContent,
            originalDisabled
        });

        element.disabled = true;
        element.innerHTML = `
            <span class="spinner spinner-sm"></span>
            <span>${text}</span>
        `;
    }

    hide(element) {
        if (!element || !this.loadingElements.has(element)) return;

        const { originalContent, originalDisabled } = this.loadingElements.get(element);
        
        element.innerHTML = originalContent;
        element.disabled = originalDisabled;
        
        this.loadingElements.delete(element);
    }
}

// ==========================================================================
// 表单验证器
// ==========================================================================

class FormValidator {
    constructor(form) {
        this.form = form;
        this.rules = new Map();
        this.setupEventListeners();
    }

    setupEventListeners() {
        this.form.addEventListener('submit', (e) => {
            if (!this.validate()) {
                e.preventDefault();
            }
        });

        // 实时验证
        this.form.addEventListener('input', (e) => {
            if (this.rules.has(e.target.name)) {
                this.validateField(e.target);
            }
        });
    }

    addRule(fieldName, validator, message) {
        if (!this.rules.has(fieldName)) {
            this.rules.set(fieldName, []);
        }
        this.rules.get(fieldName).push({ validator, message });
        return this;
    }

    required(fieldName, message = '此字段为必填项') {
        return this.addRule(fieldName, (value) => value.trim() !== '', message);
    }

    minLength(fieldName, length, message) {
        message = message || `最少需要${length}个字符`;
        return this.addRule(fieldName, (value) => value.length >= length, message);
    }

    email(fieldName, message = '请输入有效的邮箱地址') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return this.addRule(fieldName, (value) => emailRegex.test(value), message);
    }

    validateField(field) {
        const rules = this.rules.get(field.name);
        if (!rules) return true;

        let isValid = true;
        let errorMessage = '';

        for (const rule of rules) {
            if (!rule.validator(field.value)) {
                isValid = false;
                errorMessage = rule.message;
                break;
            }
        }

        this.setFieldState(field, isValid, errorMessage);
        return isValid;
    }

    validate() {
        let isFormValid = true;

        this.rules.forEach((rules, fieldName) => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (field && !this.validateField(field)) {
                isFormValid = false;
            }
        });

        return isFormValid;
    }

    setFieldState(field, isValid, message = '') {
        field.classList.remove('is-valid', 'is-invalid');
        field.classList.add(isValid ? 'is-valid' : 'is-invalid');

        // 移除之前的反馈消息
        const existingFeedback = field.parentNode.querySelector('.invalid-feedback, .valid-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        // 添加新的反馈消息
        if (message) {
            const feedback = document.createElement('div');
            feedback.className = isValid ? 'valid-feedback' : 'invalid-feedback';
            feedback.textContent = message;
            field.parentNode.appendChild(feedback);
        }
    }
}

// ==========================================================================
// 工具函数
// ==========================================================================

const Utils = {
    // 防抖函数
    debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                return true;
            } catch (err) {
                return false;
            } finally {
                document.body.removeChild(textArea);
            }
        }
    },

    // 检测设备类型
    getDeviceType() {
        const ua = navigator.userAgent;
        if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
            return 'tablet';
        }
        if (/Mobile|iP(hone|od)|Android|BlackBerry|IEMobile|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(ua)) {
            return 'mobile';
        }
        return 'desktop';
    },

    // 检测是否支持触摸
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }
};

// ==========================================================================
// 全局实例
// ==========================================================================

// 创建全局实例
window.modalManager = new ModalManager();
window.messageManager = new MessageManager();
window.loadingManager = new LoadingManager();
window.Utils = Utils;

// 简化的全局函数
window.showModal = (selector) => {
    const modal = document.querySelector(selector);
    if (modal) window.modalManager.show(modal);
};

window.hideModal = (selector) => {
    const modal = document.querySelector(selector);
    if (modal) window.modalManager.hide(modal);
};

window.showMessage = (message, type, duration) => {
    return window.messageManager.show(message, type, duration);
};

window.showLoading = (selector, text) => {
    let element;
    if (typeof selector === 'string') {
        element = document.querySelector(selector);
    } else {
        element = selector; // 直接传入元素
    }
    if (element) {
        console.log('Showing loading for element:', element);
        window.loadingManager.show(element, text);
    } else {
        console.warn('Element not found for selector:', selector);
    }
};

window.hideLoading = (selector) => {
    let element;
    if (typeof selector === 'string') {
        element = document.querySelector(selector);
    } else {
        element = selector; // 直接传入元素
    }
    if (element) {
        console.log('Hiding loading for element:', element);
        window.loadingManager.hide(element);
    } else {
        console.warn('Element not found for selector:', selector);
    }
};

// DOM加载完成后的初始化
document.addEventListener('DOMContentLoaded', () => {
    // 自动初始化所有模态框的关闭按钮
    document.querySelectorAll('.modal-close').forEach(closeBtn => {
        closeBtn.addEventListener('click', () => {
            const modal = closeBtn.closest('.modal');
            if (modal) window.modalManager.hide(modal);
        });
    });

    // 自动初始化工具提示
    document.querySelectorAll('[data-tooltip]').forEach(element => {
        const tooltipText = element.getAttribute('data-tooltip');
        element.classList.add('tooltip');
        
        const tooltip = document.createElement('span');
        tooltip.className = 'tooltip-text';
        tooltip.textContent = tooltipText;
        element.appendChild(tooltip);
    });
});
