<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终端界面</title>
    <link rel="stylesheet" href="common.css">
    <link rel="stylesheet" href="components.css">
    <style>
        /* 页面特定样式 */
        .terminal-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-md);
        }

        .screenshot-content {
            min-height: 300px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: var(--spacing-lg);
            background-color: var(--gray-50);
        }

        .screenshot-image {
            max-width: 100%;
            max-height: 70vh;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            cursor: pointer;
            transition: var(--transition-base);
            object-fit: contain;
        }

        .screenshot-image:hover {
            transform: scale(1.02);
            box-shadow: var(--shadow-xl);
        }

        .device-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-md);
        }

        /* 改善文本对比度 */
        .text-muted {
            color: var(--gray-600) !important;
            font-weight: 500;
        }

        .card-header.bg-primary {
            background-color: var(--primary-color) !important;
            color: var(--white) !important;
        }

        .card-header h2 {
            color: var(--white) !important;
            font-weight: 600;
        }

        /* 表格样式优化 */
        .table th {
            background-color: var(--gray-100) !important;
            color: var(--gray-800) !important;
            font-weight: 600;
            border-bottom: 2px solid var(--border-color);
        }

        .table td {
            color: var(--gray-700) !important;
            font-weight: 500;
        }

        .table tbody tr:hover {
            background-color: var(--gray-100) !important;
        }

        /* 按钮样式增强 */
        .btn-success {
            background-color: var(--success-color) !important;
            border-color: var(--success-color) !important;
            color: var(--white) !important;
            font-weight: 600;
        }

        .btn-success:hover {
            background-color: var(--success-hover) !important;
            border-color: var(--success-hover) !important;
            transform: translateY(-1px);
        }

        /* 加载状态样式 */
        .loading-text {
            color: var(--info-color) !important;
            font-weight: 500;
        }

        .text-danger {
            color: var(--danger-color) !important;
            font-weight: 500;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .terminal-container {
                padding: var(--spacing-sm);
            }

            .screenshot-content {
                min-height: 250px;
                padding: var(--spacing-md);
            }

            .screenshot-image {
                max-height: 50vh;
            }

            .card-header {
                padding: var(--spacing-md);
            }

            .card-header .flex {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: stretch;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .table th,
            .table td {
                padding: var(--spacing-sm);
                font-size: var(--font-size-sm);
            }

            .text-lg {
                font-size: var(--font-size-base) !important;
            }
        }

        /* 小屏幕设备进一步优化 */
        @media (max-width: 480px) {
            .terminal-container {
                padding: var(--spacing-xs);
            }

            .card {
                margin-bottom: var(--spacing-md);
            }

            .screenshot-content {
                min-height: 200px;
                padding: var(--spacing-sm);
            }

            .screenshot-image {
                max-height: 40vh;
            }

            .card-header h2 {
                font-size: var(--font-size-base) !important;
            }

            .table-container {
                font-size: var(--font-size-xs);
            }

            .table th,
            .table td {
                padding: var(--spacing-xs);
            }
        }

        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            .text-muted {
                color: var(--gray-700) !important;
            }

            .card {
                border: 2px solid var(--border-color);
            }

            .table th {
                background-color: var(--gray-200) !important;
                color: var(--gray-900) !important;
            }

            .table td {
                color: var(--gray-900) !important;
            }
        }

        /* 深色模式优化 */
        @media (prefers-color-scheme: dark) {
            .screenshot-content {
                background-color: var(--gray-800);
            }

            .text-muted {
                color: var(--gray-400) !important;
            }

            .loading-text {
                color: var(--info-color) !important;
            }
        }

        /* 焦点可见性增强 */
        .btn:focus,
        .screenshot-image:focus {
            outline: 3px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .btn {
                min-height: 48px;
                padding: var(--spacing-md) var(--spacing-lg);
            }

            .screenshot-image {
                cursor: default;
            }

            .screenshot-image:hover {
                transform: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
<div class="terminal-container">
    <!-- 截屏区域 -->
    <div class="card mb-xl">
        <div class="card-header bg-primary text-white">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold m-0">📱 设备截屏</h2>
                <button id="refreshScreenshotBtn" class="btn btn-sm btn-success">
                    <span>🔄</span>
                    <span>刷新截屏</span>
                </button>
            </div>
        </div>
        <div class="screenshot-content" id="screenshotContent">
            <div class="text-muted text-lg">
                📱 点击刷新按钮获取设备截屏
            </div>
        </div>
    </div>

    <!-- 设备信息区域 -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold m-0">📊 设备信息</h2>
                <button id="refreshDeviceBtn" class="btn btn-sm btn-success">
                    <span>🔄</span>
                    <span>刷新信息</span>
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-container">
                <table class="table">
                    <tbody id="deviceInfoBody">
                        <tr>
                            <td colspan="2" class="text-center text-muted">📊 点击刷新按钮获取设备信息</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="api.js"></script>
<script src="utils.js"></script>
<script>
    // 检查登录状态
    if (!apiClient.checkAuth()) {
        window.top.location.href = 'login.html';
    }

    const refreshScreenshotBtn = document.getElementById('refreshScreenshotBtn');
    const refreshDeviceBtn = document.getElementById('refreshDeviceBtn');
    const screenshotContent = document.getElementById('screenshotContent');
    const deviceInfoBody = document.getElementById('deviceInfoBody');

    // 页面加载时自动获取设备信息和截屏
    document.addEventListener('DOMContentLoaded', async function() {
        console.log('Terminal page loaded, loading device info and screenshot...');
        await Promise.all([
            getDeviceInfo(),
            getScreenshot()
        ]);
    });

    // 如果页面已经加载完成，立即执行
    if (document.readyState !== 'loading') {
        console.log('Document already loaded, loading data immediately...');
        Promise.all([
            getDeviceInfo(),
            getScreenshot()
        ]);
    }

    // 刷新截屏按钮事件
    refreshScreenshotBtn.addEventListener('click', async function() {
        showLoading('#refreshScreenshotBtn', '获取中...');
        await getScreenshot();
        hideLoading('#refreshScreenshotBtn');
    });

    // 刷新设备信息按钮事件
    refreshDeviceBtn.addEventListener('click', async function() {
        showLoading('#refreshDeviceBtn', '获取中...');
        await getDeviceInfo();
        hideLoading('#refreshDeviceBtn');
    });

    // 获取设备截屏
    async function getScreenshot() {
        try {
            screenshotContent.innerHTML = `
                <div class="spinner spinner-lg"></div>
                <div class="loading-text mt-md">📱 正在获取设备截屏，请稍候...</div>
            `;

            const blob = await apiClient.getScreenshot();

            if (blob) {
                const imageUrl = URL.createObjectURL(blob);
                screenshotContent.innerHTML = `
                    <img src="${imageUrl}" alt="设备截屏" class="screenshot-image"
                         onclick="window.open('${imageUrl}', '_blank')"
                         title="点击在新窗口中查看大图"
                         tabindex="0"
                         onkeydown="if(event.key==='Enter'||event.key===' ') window.open('${imageUrl}', '_blank')" />
                `;
                console.log('Screenshot loaded successfully');
            } else {
                throw new Error('未获取到截屏数据');
            }
        } catch (error) {
            console.error('Get screenshot error:', error);
            screenshotContent.innerHTML = `
                <div class="alert alert-danger">
                    <strong>❌ 获取截屏失败</strong><br>
                    ${error.message}<br>
                    <small class="text-muted">请检查设备连接状态并重试</small>
                </div>
            `;
        }
    }

    // 获取设备信息
    async function getDeviceInfo() {
        try {
            deviceInfoBody.innerHTML = '<tr><td colspan="2" class="text-center"><div class="spinner"></div> <span class="loading-text">📊 正在获取设备信息，请稍候...</span></td></tr>';

            const response = await apiClient.getDeviceInfo();

            if (response && response.data) {
                displayDeviceInfo(response.data);
                console.log('Device info loaded successfully');
            } else {
                throw new Error('设备信息数据格式错误');
            }
        } catch (error) {
            console.error('Get device info error:', error);
            deviceInfoBody.innerHTML = `
                <tr>
                    <td colspan="2" class="text-center">
                        <div class="alert alert-danger">
                            <strong>❌ 获取设备信息失败</strong><br>
                            ${error.message}<br>
                            <small class="text-muted">请检查设备连接状态并重试</small>
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    // 显示设备信息
    function displayDeviceInfo(deviceInfo) {
        // 定义字段的显示顺序和中文标签
        const fieldOrder = [
            { key: 'id', label: '设备ID', value: deviceInfo.id },
            { key: 'name', label: '设备名称', value: deviceInfo.name },
            { key: 'type', label: '设备类型', value: deviceInfo.type },
            { key: 'ip', label: 'IP地址', value: deviceInfo.ip },
            { key: 'mac', label: 'MAC地址', value: deviceInfo.mac },
            { key: 'brand', label: '品牌', value: deviceInfo.brand },
            { key: 'model', label: '型号', value: deviceInfo.model },
            { key: 'osVersion', label: '系统版本', value: deviceInfo.osVersion },
            { key: 'sdkVersion', label: 'SDK版本', value: deviceInfo.sdkVersion },
            { key: 'cpuAbi', label: 'CPU架构', value: deviceInfo.systemArchitecture },
            { key: 'resolution', label: '屏幕分辨率', value: deviceInfo.displayMetrics && deviceInfo.displayMetrics[0] ? 
                `${deviceInfo.displayMetrics[0].widthPixels}x${deviceInfo.displayMetrics[0].heightPixels}` : '未知' },
            { key: 'imei', label: 'IMEI', value: deviceInfo.imei },
            { key: 'serial', label: '序列号', value: deviceInfo.serial },
            { key: 'cpuUsed', label: 'CPU使用率', value: deviceInfo.cpuUsed ? 
                `${(deviceInfo.cpuUsed * 100).toFixed(2)}%` : '未知' },
            { key: 'memory', label: '内存', value: deviceInfo.availableMemory && deviceInfo.totalMemory ? 
                `${(deviceInfo.availableMemory / (1024 * 1024 * 1024)).toFixed(2)}/${(deviceInfo.totalMemory / (1024 * 1024 * 1024)).toFixed(2)}GB` : '未知' },
            { key: 'storage', label: '存储空间', value: deviceInfo.availableInternalDiskSize && deviceInfo.totalInternalDiskSize ? 
                `${((deviceInfo.availableInternalDiskSize + (deviceInfo.availableExternalDiskSize || 0)) / (1024 * 1024 * 1024)).toFixed(2)}/${((deviceInfo.totalInternalDiskSize + (deviceInfo.totalExternalDiskSize || 0)) / (1024 * 1024 * 1024)).toFixed(2)}GB` : '未知' }
        ];

        let tableRows = '';

        // 按照指定顺序显示字段
        fieldOrder.forEach(field => {
            if (field.value !== undefined && field.value !== null && field.value !== '') {
                tableRows += `
                    <tr>
                        <th>${field.label}</th>
                        <td>${field.value}</td>
                    </tr>
                `;
            }
        });

        if (tableRows) {
            deviceInfoBody.innerHTML = tableRows;
        } else {
            deviceInfoBody.innerHTML = '<tr><td colspan="2">未获取到设备信息</td></tr>';
        }
    }
</script>

</body>
</html>
