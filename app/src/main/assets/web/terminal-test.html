<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终端界面 - 样式测试</title>
    <link rel="stylesheet" href="common.css">
    <link rel="stylesheet" href="components.css">
    <style>
        /* 测试样式 */
        .test-section {
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
        }
        
        .color-test {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            margin: var(--spacing-md) 0;
        }
        
        .color-sample {
            padding: var(--spacing-md);
            border-radius: var(--border-radius-base);
            min-width: 120px;
            text-align: center;
            font-weight: 500;
        }
        
        .responsive-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="terminal-container">
        <h1 class="text-xxxl font-bold text-center mb-xl">终端界面样式测试</h1>
        
        <!-- 颜色对比度测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">颜色对比度测试</h2>
            <div class="color-test">
                <div class="color-sample bg-white text-primary border">
                    主要文本 (白底黑字)
                </div>
                <div class="color-sample bg-gray-50 text-secondary">
                    次要文本 (灰底)
                </div>
                <div class="color-sample bg-primary text-white">
                    主色调按钮
                </div>
                <div class="color-sample bg-success text-white">
                    成功状态
                </div>
                <div class="color-sample bg-danger text-white">
                    错误状态
                </div>
                <div class="color-sample bg-gray-100 text-muted">
                    静默文本
                </div>
            </div>
        </div>

        <!-- 响应式布局测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">响应式布局测试</h2>
            <div class="responsive-test">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="text-lg font-semibold m-0">卡片 1</h3>
                    </div>
                    <div class="card-body">
                        <p>这是一个测试卡片，用于验证响应式布局在不同屏幕尺寸下的表现。</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h3 class="text-lg font-semibold m-0">卡片 2</h3>
                    </div>
                    <div class="card-body">
                        <p>在移动设备上，这些卡片应该堆叠显示，在桌面设备上应该并排显示。</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h3 class="text-lg font-semibold m-0">卡片 3</h3>
                    </div>
                    <div class="card-body">
                        <p>文字大小和间距也会根据屏幕尺寸进行调整。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 按钮和表单测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">按钮和表单测试</h2>
            <div class="flex flex-wrap gap-md mb-lg">
                <button class="btn btn-primary">主要按钮</button>
                <button class="btn btn-success">成功按钮</button>
                <button class="btn btn-danger">危险按钮</button>
                <button class="btn btn-outline">轮廓按钮</button>
            </div>
            
            <div class="form-group">
                <label class="form-label">测试输入框</label>
                <input type="text" class="form-control" placeholder="请输入测试内容">
            </div>
        </div>

        <!-- 表格测试 */
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">表格测试</h2>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>属性</th>
                            <th>值</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>设备名称</td>
                            <td>测试设备</td>
                            <td><span class="badge badge-success">正常</span></td>
                        </tr>
                        <tr>
                            <td>IP地址</td>
                            <td>*************</td>
                            <td><span class="badge badge-info">连接中</span></td>
                        </tr>
                        <tr>
                            <td>系统版本</td>
                            <td>Android 12</td>
                            <td><span class="badge badge-warning">更新可用</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 消息提示测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">消息提示测试</h2>
            <div class="alert alert-success mb-md">
                <strong>✅ 成功！</strong> 这是一个成功消息提示。
            </div>
            <div class="alert alert-info mb-md">
                <strong>ℹ️ 信息：</strong> 这是一个信息提示。
            </div>
            <div class="alert alert-warning mb-md">
                <strong>⚠️ 警告：</strong> 这是一个警告消息。
            </div>
            <div class="alert alert-danger">
                <strong>❌ 错误：</strong> 这是一个错误消息。
            </div>
        </div>

        <!-- 加载状态测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">加载状态测试</h2>
            <div class="flex items-center gap-md mb-md">
                <div class="spinner"></div>
                <span class="loading-text">正在加载...</span>
            </div>
            <div class="flex items-center gap-md mb-md">
                <div class="spinner spinner-lg"></div>
                <span class="loading-text">正在获取数据，请稍候...</span>
            </div>
        </div>

        <!-- 可访问性测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">可访问性测试</h2>
            <p class="mb-md">使用 Tab 键导航，测试焦点可见性：</p>
            <div class="flex flex-wrap gap-md">
                <button class="btn btn-primary" tabindex="0">可聚焦按钮 1</button>
                <button class="btn btn-secondary" tabindex="0">可聚焦按钮 2</button>
                <input type="text" class="form-control" placeholder="可聚焦输入框" style="max-width: 200px;">
                <a href="#" class="btn btn-outline" tabindex="0">可聚焦链接</a>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('样式测试页面加载完成');
            
            // 测试按钮点击
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('按钮被点击:', this.textContent);
                });
            });
        });
    </script>
</body>
</html>
