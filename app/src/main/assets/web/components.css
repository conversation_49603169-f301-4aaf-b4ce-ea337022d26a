/* ==========================================================================
   组件样式库 - 可复用的UI组件
   ========================================================================== */

/* ==========================================================================
   按钮组件
   ========================================================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: 1.5;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius-base);
    cursor: pointer;
    transition: var(--transition-colors);
    user-select: none;
    white-space: nowrap;
}

.btn:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 按钮尺寸 */
.btn-xs {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.btn-xl {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-xl);
}

/* 按钮变体 */
.btn-primary {
    color: var(--white);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-primary:active {
    background-color: var(--primary-active);
    border-color: var(--primary-active);
}

.btn-secondary {
    color: var(--white);
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--secondary-hover);
    border-color: var(--secondary-hover);
}

.btn-success {
    color: var(--white);
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
    background-color: var(--success-hover);
    border-color: var(--success-hover);
}

.btn-danger {
    color: var(--white);
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
    background-color: var(--danger-hover);
    border-color: var(--danger-hover);
}

.btn-outline {
    color: var(--primary-color);
    background-color: transparent;
    border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
    color: var(--white);
    background-color: var(--primary-color);
}

.btn-ghost {
    color: var(--text-primary);
    background-color: transparent;
    border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
    background-color: var(--gray-100);
}

/* 按钮组 */
.btn-group {
    display: inline-flex;
    border-radius: var(--border-radius-base);
    overflow: hidden;
}

.btn-group .btn {
    border-radius: 0;
    border-right-width: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius-base);
    border-bottom-left-radius: var(--border-radius-base);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius-base);
    border-bottom-right-radius: var(--border-radius-base);
    border-right-width: 1px;
}

/* ==========================================================================
   表单组件
   ========================================================================== */

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-base);
    transition: var(--transition-colors);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:disabled {
    background-color: var(--gray-50);
    opacity: 0.6;
    cursor: not-allowed;
}

.form-control::placeholder {
    color: var(--text-muted);
}

/* 表单控件尺寸 */
.form-control-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.form-control-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
}

/* 复选框和单选框 */
.form-check {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.form-check-input {
    width: 1rem;
    height: 1rem;
    margin: 0;
    cursor: pointer;
}

.form-check-label {
    margin: 0;
    cursor: pointer;
    font-weight: normal;
}

/* 表单描述文本 */
.form-text {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    line-height: var(--line-height-base);
}

/* 表单验证状态 */
.form-control.is-valid {
    border-color: var(--success-color);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.valid-feedback {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--success-color);
}

.invalid-feedback {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--danger-color);
}

/* ==========================================================================
   卡片组件
   ========================================================================== */

.card {
    background-color: var(--white);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--border-light);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    background-color: var(--gray-50);
    border-top: 1px solid var(--border-light);
}

.card-title {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-subtitle {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.card-text {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-primary);
}

.card-text:last-child {
    margin-bottom: 0;
}

/* ==========================================================================
   模态框组件
   ========================================================================== */

.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--z-modal);
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.modal.show {
    display: flex;
    opacity: 1;
}

.modal-dialog {
    position: relative;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    margin: var(--spacing-lg);
    transform: scale(0.9);
    transition: transform var(--transition-base);
}

.modal.show .modal-dialog {
    transform: scale(1);
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 100%;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.modal-title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-base);
    transition: var(--transition-colors);
}

.modal-close:hover {
    color: var(--text-primary);
    background-color: var(--gray-100);
}

.modal-body {
    padding: var(--spacing-lg);
    flex: 1;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    flex-shrink: 0;
}

/* ==========================================================================
   表格组件
   ========================================================================== */

.table-container {
    overflow-x: auto;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-light);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--white);
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.table th {
    background-color: var(--gray-50);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table tbody tr:hover {
    background-color: var(--gray-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 表格尺寸 */
.table-sm th,
.table-sm td {
    padding: var(--spacing-sm);
}

.table-lg th,
.table-lg td {
    padding: var(--spacing-lg);
}

/* ==========================================================================
   消息提示组件
   ========================================================================== */

.alert {
    padding: var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius-base);
    margin-bottom: var(--spacing-md);
}

.alert-success {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
}

.alert-danger {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
}

.alert-warning {
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
}

.alert-info {
    color: #055160;
    background-color: #d1ecf1;
    border-color: #b6effb;
}

/* ==========================================================================
   加载动画组件
   ========================================================================== */

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid var(--gray-200);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-sm {
    width: 0.75rem;
    height: 0.75rem;
    border-width: 1px;
}

.spinner-lg {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 3px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ==========================================================================
   徽章组件
   ========================================================================== */

.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1;
    border-radius: var(--border-radius-full);
}

.badge-primary {
    color: var(--white);
    background-color: var(--primary-color);
}

.badge-secondary {
    color: var(--white);
    background-color: var(--secondary-color);
}

.badge-success {
    color: var(--white);
    background-color: var(--success-color);
}

.badge-danger {
    color: var(--white);
    background-color: var(--danger-color);
}

.badge-warning {
    color: var(--gray-800);
    background-color: var(--warning-color);
}

.badge-info {
    color: var(--white);
    background-color: var(--info-color);
}

/* ==========================================================================
   工具提示
   ========================================================================== */

.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: max-content;
    max-width: 200px;
    background-color: var(--gray-800);
    color: var(--white);
    text-align: center;
    border-radius: var(--border-radius-base);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    position: absolute;
    z-index: var(--z-tooltip);
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* ==========================================================================
   响应式断点系统
   ========================================================================== */

/* 移动设备优先的响应式设计 */

/* 小型设备 (手机, 576px 及以上) */
@media (min-width: 576px) {
    .container {
        max-width: 540px;
    }

    /* 显示/隐藏工具类 */
    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }

    /* 文本对齐 */
    .text-sm-left { text-align: left !important; }
    .text-sm-center { text-align: center !important; }
    .text-sm-right { text-align: right !important; }
}

/* 中型设备 (平板, 768px 及以上) */
@media (min-width: 768px) {
    .container {
        max-width: 720px;
    }

    /* 显示/隐藏工具类 */
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }

    /* Flex方向 */
    .flex-md-row { flex-direction: row !important; }
    .flex-md-col { flex-direction: column !important; }

    /* 文本对齐 */
    .text-md-left { text-align: left !important; }
    .text-md-center { text-align: center !important; }
    .text-md-right { text-align: right !important; }

    /* 间距调整 */
    .p-md-0 { padding: 0 !important; }
    .p-md-md { padding: var(--spacing-md) !important; }
    .p-md-lg { padding: var(--spacing-lg) !important; }

    .m-md-0 { margin: 0 !important; }
    .m-md-auto { margin: auto !important; }
    .mx-md-auto { margin-left: auto !important; margin-right: auto !important; }
}

/* 大型设备 (桌面, 992px 及以上) */
@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }

    /* 显示/隐藏工具类 */
    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }

    /* Flex方向 */
    .flex-lg-row { flex-direction: row !important; }
    .flex-lg-col { flex-direction: column !important; }

    /* 文本对齐 */
    .text-lg-left { text-align: left !important; }
    .text-lg-center { text-align: center !important; }
    .text-lg-right { text-align: right !important; }
}

/* 超大型设备 (大桌面, 1200px 及以上) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }

    /* 显示/隐藏工具类 */
    .d-xl-none { display: none !important; }
    .d-xl-block { display: block !important; }
    .d-xl-flex { display: flex !important; }

    /* Flex方向 */
    .flex-xl-row { flex-direction: row !important; }
    .flex-xl-col { flex-direction: column !important; }

    /* 文本对齐 */
    .text-xl-left { text-align: left !important; }
    .text-xl-center { text-align: center !important; }
    .text-xl-right { text-align: right !important; }
}

/* 超超大型设备 (4K显示器, 1400px 及以上) */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
}

/* ==========================================================================
   移动端优化
   ========================================================================== */

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px; /* 确保触摸目标足够大 */
    }

    .form-control {
        min-height: 44px;
    }

    .modal-close {
        min-width: 44px;
        min-height: 44px;
    }
}

/* 小屏幕设备优化 */
@media (max-width: 767.98px) {
    .modal-dialog {
        width: 95%;
        margin: var(--spacing-sm);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: var(--border-radius-base);
        border-right-width: 1px;
        border-bottom-width: 0;
        margin-bottom: -1px;
    }

    .btn-group .btn:first-child {
        border-radius: var(--border-radius-base);
    }

    .btn-group .btn:last-child {
        border-radius: var(--border-radius-base);
        border-bottom-width: 1px;
        margin-bottom: 0;
    }

    .table-container {
        font-size: var(--font-size-sm);
    }

    .table th,
    .table td {
        padding: var(--spacing-sm);
    }
}

/* 高分辨率显示器优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* 确保在高DPI显示器上边框清晰 */
    .border {
        border-width: 0.5px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f8f9fa;
        --text-secondary: #dee2e6;
        --text-muted: #adb5bd;
        --bg-primary: #212529;
        --bg-secondary: #343a40;
        --bg-dark: #495057;
        --border-color: #495057;
        --border-light: #343a40;
    }

    .card {
        background-color: var(--bg-primary);
        border-color: var(--border-color);
    }

    .form-control {
        background-color: var(--bg-primary);
        border-color: var(--border-color);
        color: var(--text-primary);
    }

    .modal-content {
        background-color: var(--bg-primary);
    }

    .table {
        background-color: var(--bg-primary);
    }

    .table th {
        background-color: var(--bg-secondary);
    }

    .table tbody tr:hover {
        background-color: var(--bg-secondary);
    }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .spinner {
        animation: none;
        border-top-color: var(--primary-color);
    }
}
