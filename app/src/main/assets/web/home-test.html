<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home界面样式测试</title>
    <link rel="stylesheet" href="common.css">
    <link rel="stylesheet" href="components.css">
    <style>
        /* 测试页面样式 */
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-lg);
        }
        
        .test-section {
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            background-color: var(--white);
        }
        
        .demo-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: var(--white);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .demo-user-menu {
            gap: var(--spacing-sm);
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .demo-user-menu .btn {
            background-color: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.4);
            color: var(--white);
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            min-width: 80px;
            transition: all var(--transition-base);
        }
        
        .demo-user-menu .btn:hover {
            background-color: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.7);
            color: var(--white);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .demo-user-menu .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: var(--white);
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .demo-user-menu .btn-danger:hover {
            background-color: var(--danger-hover);
            border-color: var(--danger-hover);
            color: var(--white);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }
        
        .color-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            margin: var(--spacing-lg) 0;
        }
        
        .before-after {
            padding: var(--spacing-md);
            border-radius: var(--border-radius-base);
            text-align: center;
        }
        
        .before {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: var(--white);
        }
        
        .after {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: var(--white);
        }
        
        .old-btn {
            border-color: rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.8);
            background-color: transparent;
            font-weight: normal;
        }
        
        .new-btn {
            background-color: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.4);
            color: var(--white);
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        @media (max-width: 768px) {
            .color-comparison {
                grid-template-columns: 1fr;
            }
            
            .demo-user-menu {
                gap: var(--spacing-xs);
            }
            
            .demo-user-menu .btn {
                min-width: 70px;
                font-size: var(--font-size-sm);
                padding: var(--spacing-sm) var(--spacing-md);
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="test-container">
        <h1 class="text-xxxl font-bold text-center mb-xl">Home界面样式优化测试</h1>
        
        <!-- 用户菜单按钮测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">用户菜单按钮对比测试</h2>
            
            <div class="color-comparison">
                <div class="before-after before">
                    <h3 class="text-lg font-semibold mb-md">优化前</h3>
                    <div class="flex justify-center gap-sm flex-wrap">
                        <button class="btn btn-sm old-btn">修改密码</button>
                        <button class="btn btn-sm old-btn">修改账号</button>
                        <button class="btn btn-sm btn-danger">退出登录</button>
                    </div>
                    <p class="text-sm mt-md">文字对比度不足，难以阅读</p>
                </div>
                
                <div class="before-after after">
                    <h3 class="text-lg font-semibold mb-md">优化后</h3>
                    <div class="demo-user-menu flex">
                        <button class="btn btn-sm new-btn">🔒 修改密码</button>
                        <button class="btn btn-sm new-btn">👤 修改账号</button>
                        <button class="btn btn-sm btn-danger">🚪 退出登录</button>
                    </div>
                    <p class="text-sm mt-md">文字清晰，对比度良好</p>
                </div>
            </div>
        </div>
        
        <!-- 完整头部演示 */
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">完整头部演示</h2>
            <div class="demo-header flex items-center justify-between">
                <h1 class="text-xl font-semibold m-0">终端管理系统</h1>
                <div class="demo-user-menu flex">
                    <button class="btn btn-sm" title="修改当前账户密码" aria-label="修改密码">
                        🔒 修改密码
                    </button>
                    <button class="btn btn-sm" title="修改当前用户名" aria-label="修改账号">
                        👤 修改账号
                    </button>
                    <button class="btn btn-sm btn-danger" title="退出当前登录会话" aria-label="退出登录">
                        🚪 退出登录
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 模态框演示 */
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">模态框样式演示</h2>
            <div class="flex gap-md justify-center flex-wrap">
                <button class="btn btn-primary" onclick="showModal('#demoPasswordModal')">
                    演示修改密码弹窗
                </button>
                <button class="btn btn-secondary" onclick="showModal('#demoUsernameModal')">
                    演示修改账号弹窗
                </button>
            </div>
        </div>
        
        <!-- 可访问性测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">可访问性测试</h2>
            <p class="mb-md">使用 Tab 键导航测试焦点可见性：</p>
            <div class="demo-header flex items-center justify-between">
                <h3 class="text-lg font-semibold m-0">焦点测试</h3>
                <div class="demo-user-menu flex">
                    <button class="btn btn-sm" tabindex="0">🔒 修改密码</button>
                    <button class="btn btn-sm" tabindex="0">👤 修改账号</button>
                    <button class="btn btn-sm btn-danger" tabindex="0">🚪 退出登录</button>
                </div>
            </div>
        </div>
        
        <!-- 响应式测试 -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-lg">响应式设计测试</h2>
            <p class="mb-md">调整浏览器窗口大小查看响应式效果：</p>
            <div class="demo-header flex items-center justify-between">
                <h3 class="text-lg font-semibold m-0">响应式头部</h3>
                <div class="demo-user-menu flex">
                    <button class="btn btn-sm">🔒 修改密码</button>
                    <button class="btn btn-sm">👤 修改账号</button>
                    <button class="btn btn-sm btn-danger">🚪 退出登录</button>
                </div>
            </div>
            <p class="text-sm mt-md text-muted">
                在移动设备上，按钮会调整大小和间距，头部布局会变为垂直排列
            </p>
        </div>
    </div>

    <!-- 演示用的模态框 -->
    <div id="demoPasswordModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">🔒 修改密码</h3>
                    <button type="button" class="modal-close" onclick="hideModal('#demoPasswordModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label" for="demoOldPassword">🔑 旧密码</label>
                        <input type="password" class="form-control" id="demoOldPassword" placeholder="请输入当前密码">
                        <small class="form-text">请输入您当前使用的密码</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="demoNewPassword">🆕 新密码</label>
                        <input type="password" class="form-control" id="demoNewPassword" placeholder="请输入新密码">
                        <small class="form-text">密码长度建议6位以上</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="demoConfirmPassword">✅ 确认新密码</label>
                        <input type="password" class="form-control" id="demoConfirmPassword" placeholder="请再次输入新密码">
                        <small class="form-text">请再次输入新密码以确认</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="hideModal('#demoPasswordModal')">取消</button>
                    <button type="button" class="btn btn-primary">确认修改</button>
                </div>
            </div>
        </div>
    </div>

    <div id="demoUsernameModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">👤 修改账号</h3>
                    <button type="button" class="modal-close" onclick="hideModal('#demoUsernameModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label" for="demoCurrentUsername">👤 当前用户名</label>
                        <input type="text" class="form-control" id="demoCurrentUsername" placeholder="请输入当前用户名">
                        <small class="form-text">请输入您当前的用户名</small>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="demoNewUsername">🆕 新用户名</label>
                        <input type="text" class="form-control" id="demoNewUsername" placeholder="请输入新用户名">
                        <small class="form-text">用户名建议使用字母、数字或下划线</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="hideModal('#demoUsernameModal')">取消</button>
                    <button type="button" class="btn btn-primary">确认修改</button>
                </div>
            </div>
        </div>
    </div>

    <script src="utils.js"></script>
    <script>
        // 简单的模态框控制
        function showModal(selector) {
            const modal = document.querySelector(selector);
            if (modal) {
                modal.classList.add('show');
                modal.style.display = 'flex';
            }
        }

        function hideModal(selector) {
            const modal = document.querySelector(selector);
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 250);
            }
        }

        // 点击背景关闭模态框
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    hideModal('#' + this.id);
                }
            });
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                document.querySelectorAll('.modal.show').forEach(modal => {
                    hideModal('#' + modal.id);
                });
            }
        });

        console.log('Home界面样式测试页面加载完成');
    </script>
</body>
</html>
