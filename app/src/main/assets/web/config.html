<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置设置</title>
    <link rel="stylesheet" href="common.css">
    <link rel="stylesheet" href="components.css">
    <style>
        /* 页面特定样式 */
        .config-container {
            max-width: 800px;
            margin: 0 auto;
            padding: var(--spacing-lg);
        }

        .config-section {
            margin-bottom: var(--spacing-xl);
        }

        .section-title {
            color: var(--text-primary);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }


    </style>
</head>
<body class="bg-gray-50">
<div class="config-container">
    <h1 class="text-xxxl font-bold text-center mb-xl">配置设置</h1>

    <form id="configForm">
        <!-- 系统配置 -->
        <div class="card config-section">
            <div class="card-header">
                <h2 class="section-title text-xl font-semibold m-0">系统配置</h2>
            </div>
            <div class="card-body">
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="enableAdb" name="enableAdb">
                    <label class="form-check-label" for="enableAdb">启用ADB调试</label>
                </div>
                <p class="form-text">启用后可通过ADB连接设备进行调试</p>

                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="enableAssistant" name="enableAssistant">
                    <label class="form-check-label" for="enableAssistant">启用助手功能</label>
                </div>
                <p class="form-text">启用系统助手功能</p>
            </div>
        </div>

        <!-- 终端配置 -->
        <div class="card config-section">
            <div class="card-header">
                <h2 class="section-title text-xl font-semibold m-0">终端配置</h2>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label" for="terminalName">终端名称</label>
                    <input type="text" class="form-control" id="terminalName" name="terminalName" placeholder="请输入终端名称">
                    <p class="form-text">设备显示的终端名称</p>
                </div>

                <div class="form-group">
                    <label class="form-label" for="terminalType">终端类型</label>
                    <input type="text" class="form-control" id="terminalType" name="terminalType" placeholder="请输入终端类型">
                    <p class="form-text">终端设备类型标识</p>
                </div>

                <div class="form-group">
                    <label class="form-label" for="terminalDescription">终端描述</label>
                    <textarea class="form-control" id="terminalDescription" name="terminalDescription" placeholder="请输入终端描述" rows="3"></textarea>
                    <p class="form-text">终端设备的详细描述信息</p>
                </div>

                <div class="form-group">
                    <label class="form-label" for="terminalRemark">终端备注</label>
                    <textarea class="form-control" id="terminalRemark" name="terminalRemark" placeholder="请输入终端备注" rows="3"></textarea>
                    <p class="form-text">终端设备的备注信息</p>
                </div>

                <div class="form-group">
                    <label class="form-label" for="terminalLocation">终端位置</label>
                    <input type="text" class="form-control" id="terminalLocation" name="terminalLocation" placeholder="请输入终端位置">
                    <p class="form-text">终端设备的物理位置</p>
                </div>

                <div class="form-group">
                    <label class="form-label" for="terminalExtraInfo">终端扩展信息</label>
                    <textarea class="form-control" id="terminalExtraInfo" name="terminalExtraInfo" placeholder="请输入终端扩展信息" rows="3"></textarea>
                    <p class="form-text">终端设备的额外信息</p>
                </div>
            </div>
        </div>

        <!-- 服务器配置 -->
        <div class="card config-section">
            <div class="card-header">
                <h2 class="section-title text-xl font-semibold m-0">服务器配置</h2>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label" for="serverHostUrl">服务器地址</label>
                    <input type="url" class="form-control" id="serverHostUrl" name="serverHostUrl" placeholder="http://**************:8081">
                    <p class="form-text">服务器的完整URL地址</p>
                </div>
            </div>
        </div>

        <!-- 启动器配置 -->
        <div class="card config-section">
            <div class="card-header">
                <h2 class="section-title text-xl font-semibold m-0">启动器配置</h2>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label" for="launcherType">启动器类型</label>
                    <select class="form-control" id="launcherType" name="launcherType">
                        <option value="DEFAULT">默认</option>
                        <option value="APP">应用程序</option>
                        <option value="WEB">网页</option>
                        <option value="FILE">文件</option>
                    </select>
                    <p class="form-text">选择启动器的类型</p>
                </div>

                <div class="form-group">
                    <label class="form-label" for="launcherPath">启动器路径</label>
                    <input type="text" class="form-control" id="launcherPath" name="launcherPath" placeholder="请输入启动器路径">
                    <p class="form-text">启动器的文件路径或URL</p>
                </div>

                <div class="form-group">
                    <label class="form-label" for="launcherParams">启动器参数</label>
                    <textarea class="form-control" id="launcherParams" name="launcherParams" placeholder="请输入启动器参数" rows="3"></textarea>
                    <p class="form-text">启动器的启动参数</p>
                </div>
            </div>
        </div>

        <div class="flex gap-md justify-center mt-xl">
            <button type="button" id="loadConfigBtn" class="btn btn-secondary">
                <span>🔄</span>
                <span>加载配置</span>
            </button>
            <button type="submit" class="btn btn-primary">
                <span>💾</span>
                <span>保存配置</span>
            </button>
            <button type="button" id="saveAndRestartBtn" class="btn btn-success">
                <span>🔄</span>
                <span>保存并重启</span>
            </button>
        </div>
    </form>
</div>

<script src="api.js"></script>
<script src="utils.js"></script>
<script>
    // 检查登录状态
    if (!apiClient.checkAuth()) {
        window.top.location.href = 'login.html';
    }

    const configForm = document.getElementById('configForm');
    const loadConfigBtn = document.getElementById('loadConfigBtn');
    const saveAndRestartBtn = document.getElementById('saveAndRestartBtn');

    // 页面加载时自动加载配置
    document.addEventListener('DOMContentLoaded', async function() {
        console.log('Config page loaded, loading configuration...');
        await loadConfig();
    });

    // 加载配置按钮事件
    loadConfigBtn.addEventListener('click', async function() {
        showLoading('#loadConfigBtn', '加载中...');
        await loadConfig();
        hideLoading('#loadConfigBtn');
    });

    // 加载配置函数
    async function loadConfig() {
        try {
            const response = await apiClient.getConfig();

            console.log('Config response:', response); // 调试信息

            if (response && response.data) {
                const config = response.data;
                console.log('Config data:', config); // 调试信息

                // 系统配置
                if (config.system_config) {
                    document.getElementById('enableAdb').checked = config.system_config.enable_adb !== false;
                    document.getElementById('enableAssistant').checked = config.system_config.enable_assistant !== false;
                }

                // 终端配置
                if (config.terminal_config) {
                    document.getElementById('terminalName').value = config.terminal_config.terminal_name || '';
                    document.getElementById('terminalType').value = config.terminal_config.terminal_type || '';
                    document.getElementById('terminalDescription').value = config.terminal_config.terminal_description || '';
                    document.getElementById('terminalRemark').value = config.terminal_config.terminal_remark || '';
                    document.getElementById('terminalLocation').value = config.terminal_config.terminal_location || '';
                    document.getElementById('terminalExtraInfo').value = config.terminal_config.terminal_extra_info || '';
                }

                // 服务器配置
                if (config.server_config) {
                    document.getElementById('serverHostUrl').value = config.server_config.server_host_url || '';
                }

                // 启动器配置
                if (config.launcher_config) {
                    document.getElementById('launcherType').value = config.launcher_config.launcher_type || 'DEFAULT';
                    document.getElementById('launcherPath').value = config.launcher_config.launcher_path || '';
                    document.getElementById('launcherParams').value = config.launcher_config.launcher_params || '';
                }

                showMessage('配置加载成功', 'success');
            } else {
                console.error('Invalid config response:', response);
                showMessage('配置数据格式错误', 'danger');
            }
        } catch (error) {
            console.error('Load config error:', error);
            showMessage(`加载配置失败: ${error.message}`, 'danger');
        }
    }

    // 保存配置表单提交事件
    configForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const submitBtn = e.target.querySelector('button[type="submit"]');
        showLoading(submitBtn, '保存中...');
        await saveConfig(false);
        hideLoading(submitBtn);
    });

    // 保存并重启按钮事件
    saveAndRestartBtn.addEventListener('click', async function() {
        showLoading('#saveAndRestartBtn', '保存中...');
        await saveConfig(true);
        hideLoading('#saveAndRestartBtn');
    });

    // 保存配置函数
    async function saveConfig(autoRestart = false) {
        try {
            
            const configData = {
                system_config: {
                    enable_adb: document.getElementById('enableAdb').checked,
                    enable_assistant: document.getElementById('enableAssistant').checked
                },
                terminal_config: {
                    terminal_name: document.getElementById('terminalName').value,
                    terminal_type: document.getElementById('terminalType').value,
                    terminal_description: document.getElementById('terminalDescription').value,
                    terminal_remark: document.getElementById('terminalRemark').value,
                    terminal_location: document.getElementById('terminalLocation').value,
                    terminal_extra_info: document.getElementById('terminalExtraInfo').value
                },
                server_config: {
                    server_host_url: document.getElementById('serverHostUrl').value
                },
                launcher_config: {
                    launcher_type: document.getElementById('launcherType').value,
                    launcher_path: document.getElementById('launcherPath').value,
                    launcher_params: document.getElementById('launcherParams').value
                }
            };
            
            const response = await apiClient.setConfig(configData, autoRestart);

            if (response && response.data) {
                if (autoRestart) {
                    showMessage('配置保存成功，系统将在3秒后重启', 'success');
                } else {
                    showMessage(response.data, 'success');
                }
            } else {
                showMessage('保存配置成功', 'success');
            }
        } catch (error) {
            showMessage(`保存配置失败: ${error.message}`, 'danger');
        }
    }
</script>

</body>
</html>
