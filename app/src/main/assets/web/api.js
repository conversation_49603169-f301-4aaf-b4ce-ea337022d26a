// api.js - 网络请求处理文件

// 统一的fetch错误处理函数
function handleFetchError(response) {
    if (response.status === 403) {
        // 认证失败，清除本地存储并跳转到登录页面
        localStorage.removeItem('sessionId');
        window.top.location.href = 'login.html';
        return Promise.reject(new Error('认证失败'));
    }
    return response;
}

// API请求类
class ApiClient {
    constructor() {
        this.sessionId = localStorage.getItem('sessionId') ||
                         (window.parent ? window.parent.localStorage.getItem('sessionId') : null);
        this.headers = {
            'Authorization': this.sessionId
        };
    }

    // 检查认证状态
    checkAuth() {
        if (!this.sessionId) {
            // 只有在非登录页面时才跳转
            if (!window.location.pathname.includes('login.html')) {
                window.top.location.href = 'login.html';
            }
            return false;
        }
        return true;
    }

    // 通用GET请求
    async get(url) {
        if (!this.checkAuth()) return;

        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: this.headers
            });
            return await handleFetchError(response).json();
        } catch (error) {
            throw new Error(`请求失败: ${error.message}`);
        }
    }

    // 通用POST请求
    async post(url, data, contentType = 'application/json') {
        if (!this.checkAuth()) return;

        const headers = { ...this.headers };
        if (contentType) {
            headers['Content-Type'] = contentType;
        }

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: contentType === 'application/json' ? JSON.stringify(data) : data
            });
            return await handleFetchError(response).json();
        } catch (error) {
            throw new Error(`请求失败: ${error.message}`);
        }
    }

    // 通用GET请求返回Blob
    async getBlob(url) {
        if (!this.checkAuth()) return;

        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: this.headers
            });
            return await handleFetchError(response).blob();
        } catch (error) {
            throw new Error(`请求失败: ${error.message}`);
        }
    }

    // 登录
    async login(username, password) {
        try {
            const response = await fetch(`/api/auth/login?username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`);
            if (response.ok) {
                return await response.json();
            } else {
                throw new Error('登录失败');
            }
        } catch (error) {
            throw new Error(`登录失败: ${error.message}`);
        }
    }

    // 登出
    async logout() {
        try {
            const response = await fetch('/api/auth/logout', {
                method: 'GET',
                headers: this.headers
            });
            return await handleFetchError(response);
        } catch (error) {
            throw new Error(`登出失败: ${error.message}`);
        }
    }

    // 修改密码
    async changePassword(oldPassword, newPassword) {
        try {
            const response = await fetch(`/api/auth/changePassword?oldPassword=${encodeURIComponent(oldPassword)}&newPassword=${encodeURIComponent(newPassword)}`, {
                method: 'GET',
                headers: this.headers
            });
            return await handleFetchError(response).json();
        } catch (error) {
            throw new Error(`修改密码失败: ${error.message}`);
        }
    }

    // 修改用户名
    async changeUsername(oldUsername, newUsername) {
        try {
            const response = await fetch(`/api/auth/changeUsername?oldUsername=${encodeURIComponent(oldUsername)}&newUsername=${encodeURIComponent(newUsername)}`, {
                method: 'GET',
                headers: this.headers
            });
            return await handleFetchError(response).json();
        } catch (error) {
            throw new Error(`修改用户名失败: ${error.message}`);
        }
    }

    // 获取设备信息
    async getDeviceInfo() {
        return await this.get('/api/common/getDeviceInfo');
    }

    // 获取屏幕截图
    async getScreenshot() {
        return await this.getBlob('/api/common/screenshot');
    }

    // 安装APK
    async installApk(formData) {
        return await this.post('/api/common/installApk', formData, null);
    }

    // 替换开机动画
    async replaceBootAnimation(formData) {
        return await this.post('/api/common/replaceBootAnimation', formData, null);
    }

    // 重启设备
    async reboot(delayMillis = 3000) {
        return await this.get(`/api/common/reboot?delayMillis=${delayMillis}`);
    }

    // 关机
    async shutdown(delayMillis = 3000) {
        return await this.get(`/api/common/shutdown?delayMillis=${delayMillis}`);
    }

    // 网络ADB控制
    async networkAdb(open, port = 5555) {
        return await this.get(`/api/common/networkAdb?open=${open}&port=${port}`);
    }

    // 获取启动器设置
    async getLauncher() {
        return await this.get('/api/launcher');
    }

    // 设置启动器
    async setLauncher(data) {
        return await this.post('/api/launcher', data);
    }

    // 获取配置信息
    async getConfig() {
        return await this.get('/api/config');
    }

    // 设置配置信息
    async setConfig(data, autoRestart = false) {
        const url = `/api/config?autoRoot=${autoRestart}`;
        return await this.post(url, data);
    }
}

// 创建API客户端实例
const apiClient = new ApiClient();
