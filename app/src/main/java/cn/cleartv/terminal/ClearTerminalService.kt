package cn.cleartv.terminal

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import cn.cleartv.terminal.assistant.AssistantService
import cn.cleartv.terminal.localserver.HttpServer
import cn.cleartv.terminal.localserver.NsdServer
import cn.cleartv.terminal.log.Log
import cn.cleartv.terminal.network.HeartbeatService
import cn.cleartv.terminal.platform.PlatformManager
import cn.cleartv.terminal.ui.PermissionRequestActivity.Companion.startOverlayService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

class ClearTerminalService : Service() {


  private val httpPort = 8888

  private val scope = CoroutineScope(Dispatchers.IO + Job())

  private val httpServer = HttpServer(httpPort)

  override fun onCreate() {
    super.onCreate()
    Log.i("onCreate")
    startForeground()
    // 开启Http服务
    httpServer.start()

    // 开启NSD服务
    NsdServer.registerService(httpPort)

    // 开启心跳
    HeartbeatService.start()

    // 开启悬浮按钮
    configDataStore.getData(SystemConfig.ENABLE_ASSISTANT, default = SystemConfig.enableAssistant)
      .onEach {
        if(it) {
          startOverlayService(AssistantService::class.java)
        } else {
          stopService(Intent(this, AssistantService::class.java))
        }
      }.launchIn(scope)

    // 监听ADB开关
    configDataStore.getData(SystemConfig.ENABLE_ADB, SystemConfig.enableAdb)
      .onEach {
        PlatformManager.platform.openNetworkAdb(it)
      }.launchIn(scope)

  }

  override fun onDestroy() {
    super.onDestroy()
    httpServer.stop()
    NsdServer.unregisterService()
    HeartbeatService.destroy()
    scope.cancel()
  }

  override fun onBind(intent: Intent?): IBinder? {
    return null
  }

  private fun startForeground() {
    val channelId = "clear_terminal_channel"
    val channelName = "Clear Terminal"

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      val channel = NotificationChannel(
        channelId,
        channelName,
        NotificationManager.IMPORTANCE_LOW
      )
      val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
      manager.createNotificationChannel(channel)
    }

    val notification = NotificationCompat.Builder(this, channelId)
      .setContentTitle("Clear Terminal Service")
      .setContentText("服务正在运行")
      .setSmallIcon(R.drawable.ic_default)
      .build()

    startForeground(1, notification)
  }

}