package cn.cleartv.terminal.network

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import cn.cleartv.terminal.App
import cn.cleartv.terminal.DeviceInfo
import cn.cleartv.terminal.ServerConfig
import cn.cleartv.terminal.configDataStore
import cn.cleartv.terminal.log.Log
import cn.cleartv.terminal.network.NetworkManager.DEFAULT_SERVER
import cn.cleartv.terminal.network.api.TerminalApiService
import cn.cleartv.terminal.network.model.TerminalAuthRequest
import cn.cleartv.terminal.network.model.TerminalAuthResponse
import cn.cleartv.terminal.network.model.TerminalHeartbeatRequest
import cn.cleartv.terminal.network.model.TerminalRegisterRequest
import cn.cleartv.terminal.network.model.TerminalResponse
import cn.cleartv.terminal.network.model.TerminalSelfUpdateRequest
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory

/**
 * 终端服务类
 * 提供终端相关的所有网络操作
 */
object TerminalService {

  private const val SP_NAME = "auth_info"
  private const val KEY_AUTH_PASSWORD = "auth_password"
  private const val KEY_ACCESS_TOKEN = "access_token"
  private const val KEY_TOKEN_EXPIRES_AT = "token_expires_at"

  private const val TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000L // 5分钟
  private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

  // token刷新任务
  private var refreshJob: Job? = null

  val terminalId: String = DeviceInfo.id
  var hostUrl = ServerConfig.hostUrl

  init {
    // 监听服务器地址变化
    configDataStore.getData(ServerConfig.SERVER_HOST_KEY, ServerConfig.hostUrl)
      .onEach {
        hostUrl = it
      }.launchIn(scope)
  }

  private val prefs: SharedPreferences by lazy {
    App.instance.getSharedPreferences(
      SP_NAME,
      Context.MODE_PRIVATE
    )
  }

  private var token: String? = prefs.getString(KEY_ACCESS_TOKEN, null)
  private var expiresTime: Long = prefs.getLong(KEY_TOKEN_EXPIRES_AT, 0)

  // API服务
  private val apiService: TerminalApiService = Retrofit.Builder()
    .baseUrl(DEFAULT_SERVER)
    .client(
      NetworkManager.createOkhttpClient(
        arrayOf(
          NetworkManager.DomainInterceptor { hostUrl },
          NetworkManager.AuthInterceptor({ token }) { clearAuthInfo() },
        )
      )
    )
    .addConverterFactory(GsonConverterFactory.create())
    .build().create(TerminalApiService::class.java)

  // 终端信息状态
  private val _terminalInfo = MutableStateFlow<TerminalResponse?>(null)
  val terminalInfo: StateFlow<TerminalResponse?> = _terminalInfo.asStateFlow()

  // 网络状态
  private val _networkState = MutableStateFlow<NetworkState>(NetworkState.Idle)
  val networkState: StateFlow<NetworkState> = _networkState.asStateFlow()

  // 认证状态
  private val _authState = MutableStateFlow<AuthState>(AuthState.NotAuthenticated)
  val authState: StateFlow<AuthState> = _authState.asStateFlow()

  /**
   * 认证状态枚举
   */
  sealed class AuthState {
    data object NotAuthenticated : AuthState()
    data object Authenticating : AuthState()
    data class Authenticated(val token: String, val expiresAt: Long) : AuthState()
    data class AuthenticationFailed(val error: String) : AuthState()
  }

  /**
   * 网络状态枚举
   */
  sealed class NetworkState {
    data object Idle : NetworkState()
    data object Loading : NetworkState()
    data class Success(val message: String) : NetworkState()
    data class Error(val error: String) : NetworkState()
  }

  suspend fun authenticate(): Boolean {
    _authState.value = AuthState.Authenticating
    var authPassword = prefs.getString(KEY_AUTH_PASSWORD, null)
    if (authPassword.isNullOrEmpty()) {
      try {
        val request = TerminalRegisterRequest(terminalId)
        val response = apiService.registerTerminal(request)
        Log.d("registerTerminal: $response")
        if (response.isSuccessful) {
          val result = response.body()
          Log.d("result: $result")
          if (result?.code == 200 && result.data != null) {
            // 保存认证信息
            authPassword = result.data.authPassword
            prefs.edit {
              putString(KEY_AUTH_PASSWORD, authPassword)
            }
            Log.d("registerTerminal success: $terminalId, $authPassword")
          } else {
            val errorMsg = result?.message ?: "注册失败"
            _authState.value = AuthState.AuthenticationFailed(errorMsg)
            return false
          }
        } else {
          val errorMsg = "网络请求失败: ${response.code()}"
          _authState.value = AuthState.AuthenticationFailed(errorMsg)
          return false
        }
      } catch (e: Exception) {
        val errorMsg = "注册异常: ${e.message}"
        _authState.value = AuthState.AuthenticationFailed(errorMsg)
        return false
      }
    }

    if (terminalId.isEmpty() || authPassword.isEmpty()) {
      _authState.value = AuthState.AuthenticationFailed("缺少终端认证信息")
      return false
    }

    return try {
      val request = TerminalAuthRequest(terminalId, authPassword)
      val response = apiService.authenticateTerminal(request)

      if (response.isSuccessful) {
        val result = response.body()
        if (result?.code == 200 && result.data?.success == true) {
          val authResponse = result.data
          // 保存token信息
          saveTokenInfo(authResponse)
          startTokenRefreshTimer()
          _authState.value = AuthState.Authenticated(
            authResponse.token ?: "",
            System.currentTimeMillis() + (authResponse.expiresIn ?: 0) * 1000
          )
          true
        } else {
          _authState.value = AuthState.AuthenticationFailed(result?.message ?: "认证失败")
          false
        }
      } else {
        _authState.value =
          AuthState.AuthenticationFailed("网络请求失败: ${response.code()}")
        false
      }
    } catch (e: Exception) {
      _authState.value = AuthState.AuthenticationFailed("认证异常: ${e.message}")
      false
    }
  }


  /**
   * 保存token信息
   */
  private fun saveTokenInfo(authResponse: TerminalAuthResponse) {
    val expiresAt = System.currentTimeMillis() + (authResponse.expiresIn ?: 0) * 1000
    prefs.edit {
      putString(KEY_ACCESS_TOKEN, authResponse.token)
        .putLong(KEY_TOKEN_EXPIRES_AT, expiresAt)
    }
    token = authResponse.token
    expiresTime = expiresAt
  }

  suspend fun refreshToken(): Boolean {
    if (token.isNullOrEmpty()) {
      return authenticate()
    }

    return try {
      val response = apiService.refreshToken("Bearer $token")

      if (response.isSuccessful) {
        val result = response.body()
        if (result?.code == 200 && result.data?.success == true) {
          val authResponse = result.data
          saveTokenInfo(authResponse)
          _authState.value = AuthState.Authenticated(
            authResponse.token ?: "",
            System.currentTimeMillis() + (authResponse.expiresIn ?: 0) * 1000
          )
          true
        } else {
          // token刷新失败，尝试重新认证
          authenticate()
        }
      } else {
        // token刷新失败，尝试重新认证
        authenticate()
      }
    } catch (e: Exception) {
      // token刷新异常，尝试重新认证
      authenticate()
    }
  }

  /**
   * 检查是否已认证
   */
  fun isAuthenticated(): Boolean {
    return token != null && System.currentTimeMillis() < expiresTime
  }

  /**
   * 发送心跳
   */
  suspend fun sendHeartbeat(request: TerminalHeartbeatRequest): ApiResult<String> {
    return withContext(Dispatchers.IO) {
      try {
        if (!isAuthenticated()) {
          val authResult = authenticate()
          if (!authResult) {
            return@withContext ApiResult.Error("认证失败，无法发送心跳")
          }
        }

        if (token.isNullOrEmpty()) {
          return@withContext ApiResult.Error("无效的认证token")
        }

        val response = apiService.heartbeat("Bearer $token", request)

        if (response.isSuccessful) {
          val result = response.body()
          if (result?.code == 200) {
            ApiResult.Success("心跳发送成功")
          } else {
            ApiResult.Error(result?.message ?: "心跳发送失败")
          }
        } else {
          ApiResult.Error("网络请求失败: ${response.code()}")
        }
      } catch (e: Exception) {
        ApiResult.Error("心跳发送异常: ${e.message}")
      }
    }
  }

  private fun startTokenRefreshTimer() {
    refreshJob?.cancel()
    refreshJob = scope.launch {
      while (isActive) {
        val expiresAt = prefs.getLong(KEY_TOKEN_EXPIRES_AT, 0)
        val currentTime = System.currentTimeMillis()
        val timeUntilRefresh = expiresAt - currentTime - TOKEN_REFRESH_THRESHOLD

        if (timeUntilRefresh > 0) {
          delay(timeUntilRefresh)
        }

        if (isActive) {
          refreshToken()
        }
      }
    }
  }

  suspend fun checkTokenValidity(): Boolean {
    if (!isAuthenticated()) {
      val authResult = authenticate()
      if (!authResult) {
        return false
      }
    }

    if (token.isNullOrEmpty()) {
      return false
    }
    return true
  }

  /**
   * 获取终端信息
   */
  suspend fun getTerminalInfo(): ApiResult<TerminalResponse> {
    return withContext(Dispatchers.IO) {
      try {
        _networkState.value = NetworkState.Loading

        if (!checkTokenValidity()) {
          _networkState.value = NetworkState.Error("认证失败")
          return@withContext ApiResult.Error("认证失败，无法获取终端信息")
        }

        val response = apiService.getTerminalInfo("Bearer $token")

        if (response.isSuccessful) {
          val result = response.body()
          if (result?.code == 200 && result.data != null) {
            _terminalInfo.value = result.data
            _networkState.value = NetworkState.Success("获取终端信息成功")
            ApiResult.Success(result.data)
          } else {
            val errorMsg = result?.message ?: "获取终端信息失败"
            _networkState.value = NetworkState.Error(errorMsg)
            ApiResult.Error(errorMsg)
          }
        } else {
          val errorMsg = "网络请求失败: ${response.code()}"
          _networkState.value = NetworkState.Error(errorMsg)
          ApiResult.Error(errorMsg)
        }
      } catch (e: Exception) {
        val errorMsg = "获取终端信息异常: ${e.message}"
        _networkState.value = NetworkState.Error(errorMsg)
        ApiResult.Error(errorMsg)
      }
    }
  }

  /**
   * 上报终端状态
   */
  suspend fun reportStatus(request: TerminalHeartbeatRequest): ApiResult<String> {
    return withContext(Dispatchers.IO) {
      try {
        _networkState.value = NetworkState.Loading

        if (!checkTokenValidity()) {
          _networkState.value = NetworkState.Error("认证失败")
          return@withContext ApiResult.Error("认证失败，无法获取终端信息")
        }

        val response = apiService.reportStatus("Bearer $token", request)

        if (response.isSuccessful) {
          val result = response.body()
          if (result?.code == 200) {
            ApiResult.Success("状态上报成功")
          } else {
            ApiResult.Error(result?.message ?: "状态上报失败")
          }
        } else {
          ApiResult.Error("网络请求失败: ${response.code()}")
        }
      } catch (e: Exception) {
        ApiResult.Error("状态上报异常: ${e.message}")
      }
    }
  }

  /**
   * 更新终端自身信息
   */
  suspend fun updateTerminalSelf(request: TerminalSelfUpdateRequest): ApiResult<TerminalResponse> {
    return withContext(Dispatchers.IO) {
      try {
        _networkState.value = NetworkState.Loading

        if (!checkTokenValidity()) {
          _networkState.value = NetworkState.Error("认证失败")
          return@withContext ApiResult.Error("认证失败，无法获取终端信息")
        }

        val response = apiService.updateTerminalSelf("Bearer $token", request)

        if (response.isSuccessful) {
          val result = response.body()
          if (result?.code == 200 && result.data != null) {
            _terminalInfo.value = result.data
            _networkState.value = NetworkState.Success("终端信息更新成功")
            ApiResult.Success(result.data)
          } else {
            val errorMsg = result?.message ?: "更新终端信息失败"
            _networkState.value = NetworkState.Error(errorMsg)
            ApiResult.Error(errorMsg)
          }
        } else {
          val errorMsg = "网络请求失败: ${response.code()}"
          _networkState.value = NetworkState.Error(errorMsg)
          ApiResult.Error(errorMsg)
        }
      } catch (e: Exception) {
        val errorMsg = "更新终端信息异常: ${e.message}"
        _networkState.value = NetworkState.Error(errorMsg)
        ApiResult.Error(errorMsg)
      }
    }
  }

  fun clearAuthInfo() {
    prefs.edit {
      remove(KEY_ACCESS_TOKEN)
        .remove(KEY_TOKEN_EXPIRES_AT)
    }
    refreshJob?.cancel()
    _authState.value = AuthState.NotAuthenticated
  }

  /**
   * 登出
   */
  fun logout() {
    clearAuthInfo()
    _terminalInfo.value = null
    _networkState.value = NetworkState.Idle
  }

  /**
   * 销毁服务
   */
  fun destroy() {
    scope.cancel()
  }
}

/**
 * API结果封装类
 */
sealed class ApiResult<out T> {
  data class Success<T>(val data: T) : ApiResult<T>()
  data class Error(val message: String) : ApiResult<Nothing>()

  fun isSuccess(): Boolean = this is Success
  fun isError(): Boolean = this is Error

  fun getOrNull(): T? = when (this) {
    is Success -> data
    is Error -> null
  }

  fun getErrorMessage(): String? = when (this) {
    is Success -> null
    is Error -> message
  }
}