package cn.cleartv.terminal.network.api

import cn.cleartv.terminal.network.model.Result
import cn.cleartv.terminal.network.model.TerminalAuthRequest
import cn.cleartv.terminal.network.model.TerminalAuthResponse
import cn.cleartv.terminal.network.model.TerminalHeartbeatRequest
import cn.cleartv.terminal.network.model.TerminalRegisterRequest
import cn.cleartv.terminal.network.model.TerminalRegisterResponse
import cn.cleartv.terminal.network.model.TerminalResponse
import cn.cleartv.terminal.network.model.TerminalSelfUpdateRequest
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.PUT

/**
 * 终端API服务接口
 * 对应服务端的TerminalApiController
 */
interface TerminalApiService {

  /**
   * 终端注册
   * 终端设备自动注册接口，不需要权限验证
   */
  @POST("/api/terminal/register")
  suspend fun registerTerminal(
    @Body request: TerminalRegisterRequest
  ): Response<Result<TerminalRegisterResponse>>

  /**
   * 终端认证
   * 终端使用注册时获得的密码进行认证，获取访问token
   */
  @POST("/api/terminal/auth")
  suspend fun authenticateTerminal(
    @Body request: TerminalAuthRequest
  ): Response<Result<TerminalAuthResponse>>

  /**
   * 终端心跳
   * 终端定期发送心跳保持连接，需要token认证
   */
  @POST("/api/terminal/heartbeat")
  suspend fun heartbeat(
    @Header("Authorization") authorization: String,
    @Body request: TerminalHeartbeatRequest
  ): Response<Result<String>>

  /**
   * 获取终端自身信息
   * 终端通过token获取自身的详细信息
   */
  @GET("/api/terminal/info")
  suspend fun getTerminalInfo(
    @Header("Authorization") authorization: String
  ): Response<Result<TerminalResponse>>

  /**
   * 终端状态上报
   * 终端主动上报自身状态信息
   */
  @POST("/api/terminal/status")
  suspend fun reportStatus(
    @Header("Authorization") authorization: String,
    @Body request: TerminalHeartbeatRequest
  ): Response<Result<String>>

  /**
   * 刷新token
   * 终端在token即将过期时刷新token
   */
  @POST("/api/terminal/refresh-token")
  suspend fun refreshToken(
    @Header("Authorization") authorization: String
  ): Response<Result<TerminalAuthResponse>>

  /**
   * 终端自身信息更新
   * 终端设备更新自身的基础信息
   */
  @PUT("/api/terminal/update")
  suspend fun updateTerminalSelf(
    @Header("Authorization") authorization: String,
    @Body request: TerminalSelfUpdateRequest
  ): Response<Result<TerminalResponse>>
}