package cn.cleartv.terminal.network.model

import android.os.Build
import android.os.SystemClock
import cn.cleartv.terminal.utils.TimeUtils
import cn.cleartv.terminal.DeviceInfo
import cn.cleartv.terminal.TerminalConfig
import cn.cleartv.terminal.configs
import cn.cleartv.terminal.packageInfo
import cn.cleartv.terminal.toJson
import cn.cleartv.terminal.utils.DeviceUtils
import cn.cleartv.terminal.utils.NetworkUtils
import com.google.gson.annotations.SerializedName

/**
 * 统一响应结果类
 */
data class Result<T>(
  @SerializedName("code")
  val code: Int,

  @SerializedName("message")
  val message: String,

  @SerializedName("data")
  val data: T? = null,

  @SerializedName("timestamp")
  val timestamp: Long = System.currentTimeMillis()
)

private val cpuInfoString
  get() = mapOf(
    "cpuUsed" to DeviceInfo.cpuUsed
  ).toJson()

private val memoryInfoString
  get() = mapOf(
    "usedMemory" to DeviceInfo.usedMemory,
    "availableMemory" to DeviceInfo.availableMemory,
    "totalMemory" to DeviceInfo.totalMemory
  ).toJson()

private val diskInfoString
  get() = mapOf(
    "availableInternalDiskSize" to DeviceInfo.availableInternalDiskSize,
    "totalInternalDiskSize" to DeviceInfo.totalInternalDiskSize,
    "availableExternalDiskSize" to DeviceInfo.availableExternalDiskSize,
    "availableExternalDiskSize" to DeviceInfo.availableExternalDiskSize
  ).toJson()

private val screenInfoString get() = DeviceInfo.displayMetrics.toJson()

/**
 * 终端注册请求
 */
data class TerminalRegisterRequest(
  @SerializedName("terminalId")
  val terminalId: String,

  @SerializedName("name")
  val name: String = TerminalConfig.name,

  @SerializedName("type")
  val type: String? = TerminalConfig.type,

  @SerializedName("version")
  val version: String? = "${packageInfo.versionName}(${packageInfo.versionCode})",

  @SerializedName("ipAddress")
  val ipAddress: String? = NetworkUtils.getIPAddress(),

  @SerializedName("macAddress")
  val macAddress: String? = DeviceUtils.getMacAddress(),

  @SerializedName("location")
  val location: String? = null,

  @SerializedName("config")
  val config: String? = configs.toJson(),

  @SerializedName("extraInfo")
  val extraInfo: String? = TerminalConfig.extraInfo,

  @SerializedName("osType")
  val osType: String? = "Android",

  @SerializedName("osVersion")
  val osVersion: String? = "Android ${Build.VERSION.RELEASE}",

  @SerializedName("cpuInfo")
  val cpuInfo: String? = cpuInfoString,

  @SerializedName("memoryInfo")
  val memoryInfo: String? = memoryInfoString,

  @SerializedName("diskInfo")
  val diskInfo: String? = diskInfoString,

  @SerializedName("screenInfo")
  val screenInfo: String? = screenInfoString,

  @SerializedName("systemArchitecture")
  val systemArchitecture: String? = Build.SUPPORTED_ABIS.joinToString(",")
)

/**
 * 终端注册响应
 */
data class TerminalRegisterResponse(
  @SerializedName("id")
  val id: Long,

  @SerializedName("terminalId")
  val terminalId: String,

  @SerializedName("name")
  val name: String,

  @SerializedName("authPassword")
  val authPassword: String,

  @SerializedName("createTime")
  val createTime: String?
)

/**
 * 终端认证请求
 */
data class TerminalAuthRequest(
  @SerializedName("terminalId")
  val terminalId: String,

  @SerializedName("authPassword")
  val authPassword: String
)

/**
 * 终端认证响应
 */
data class TerminalAuthResponse(
  @SerializedName("success")
  val success: Boolean,

  @SerializedName("message")
  val message: String,

  @SerializedName("token")
  val token: String?,

  @SerializedName("expiresIn")
  val expiresIn: Long?,

  @SerializedName("terminal")
  val terminal: TerminalResponse?
)

/**
 * 终端心跳请求
 */
data class TerminalHeartbeatRequest(
  @SerializedName("terminalId")
  val terminalId: String = DeviceInfo.id,

  @SerializedName("status")
  val status: Int? = 1,

  @SerializedName("cpuInfo")
  val cpuInfo: String? = cpuInfoString,

  @SerializedName("memoryInfo")
  val memoryInfo: String? = memoryInfoString,

  @SerializedName("diskInfo")
  val diskInfo: String? = diskInfoString,

  @SerializedName("systemBootTime")
  val systemBootTime: String? = TimeUtils.millis2String(
    System.currentTimeMillis() - SystemClock.elapsedRealtime(),
    "yyyy-MM-dd'T'HH:mm:ss"
  ),

  @SerializedName("systemUptime")
  val systemUptime: Long? = SystemClock.elapsedRealtime()
)

/**
 * 终端响应信息
 */
data class TerminalResponse(
  @SerializedName("id")
  val id: Long?,

  @SerializedName("terminalId")
  val terminalId: String?,

  @SerializedName("name")
  val name: String?,

  @SerializedName("description")
  val description: String?,

  @SerializedName("type")
  val type: String?,

  @SerializedName("version")
  val version: String?,

  @SerializedName("ipAddress")
  val ipAddress: String?,

  @SerializedName("macAddress")
  val macAddress: String?,

  @SerializedName("location")
  val location: String?,

  @SerializedName("status")
  val status: Int?,

  @SerializedName("activeStatus")
  val activeStatus: Int?,

  @SerializedName("lastHeartbeat")
  val lastHeartbeat: String?,

  @SerializedName("config")
  val config: String?,

  @SerializedName("extraInfo")
  val extraInfo: String?,

  @SerializedName("osType")
  val osType: String?,

  @SerializedName("osVersion")
  val osVersion: String?,

  @SerializedName("cpuInfo")
  val cpuInfo: String?,

  @SerializedName("memoryInfo")
  val memoryInfo: String?,

  @SerializedName("diskInfo")
  val diskInfo: String?,

  @SerializedName("screenInfo")
  val screenInfo: String?,

  @SerializedName("systemArchitecture")
  val systemArchitecture: String?,

  @SerializedName("systemBootTime")
  val systemBootTime: String?,

  @SerializedName("systemUptime")
  val systemUptime: Long?,

  @SerializedName("creatorId")
  val creatorId: Long?,

  @SerializedName("updaterId")
  val updaterId: Long?,

  @SerializedName("remark")
  val remark: String?,

  @SerializedName("createTime")
  val createTime: String?,

  @SerializedName("updateTime")
  val updateTime: String?
)

/**
 * 终端控制请求
 */
data class TerminalControlRequest(
  @SerializedName("terminalId")
  val terminalId: String,

  @SerializedName("command")
  val command: String,

  @SerializedName("params")
  val params: Map<String, String>? = null
)

/**
 * 终端激活请求
 */
data class TerminalActivateRequest(
  @SerializedName("terminalIds")
  val terminalIds: List<Long>,

  @SerializedName("activeStatus")
  val activeStatus: Int
)

/**
 * 终端自身信息更新请求
 * 供终端设备更新自身信息使用，只包含终端可以自主更新的字段
 */
data class TerminalSelfUpdateRequest(
  @SerializedName("version")
  val version: String? = null,

  @SerializedName("ipAddress")
  val ipAddress: String? = null,

  @SerializedName("macAddress")
  val macAddress: String? = null,

  @SerializedName("location")
  val location: String? = null,

  @SerializedName("config")
  val config: String? = null,

  @SerializedName("extraInfo")
  val extraInfo: String? = null,

  @SerializedName("osType")
  val osType: String? = null,

  @SerializedName("osVersion")
  val osVersion: String? = null,

  @SerializedName("screenInfo")
  val screenInfo: String? = null,

  @SerializedName("systemArchitecture")
  val systemArchitecture: String? = null
)