package cn.cleartv.terminal.network

import cn.cleartv.terminal.utils.SslUtils
import cn.cleartv.terminal.isDebug
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit

object NetworkManager {

  const val DEFAULT_SERVER = "https://localhost"
  private const val CONNECT_TIMEOUT = 30L
  private const val READ_TIMEOUT = 30L
  private const val WRITE_TIMEOUT = 30L

  class AuthInterceptor(val tokenProvider: () -> String?, val onUnauthorized: () -> Unit) :
    Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
      val originalRequest = chain.request()

      // 获取token
      val token = tokenProvider()

      // 如果有token，添加到请求头
      val newRequest = if (!token.isNullOrEmpty()) {
        originalRequest.newBuilder()
          .header("Authorization", "Bearer $token")
          .build()
      } else {
        originalRequest
      }
      val response = chain.proceed(newRequest)
      if (response.code == 401) {
        onUnauthorized.invoke()
      }
      return response
    }

  }

  class DomainInterceptor(val hostProvider: () -> String) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
      val request = chain.request()
      return chain.proceed(
        request.newBuilder()
          .url(
            request.url.toString()
              .replace(DEFAULT_SERVER, hostProvider())
              .toHttpUrlOrNull() ?: request.url
          )
          .build()
      )
    }

  }

  fun createOkhttpClient(
    interceptors: Array<Interceptor> = emptyArray()
  ): OkHttpClient {
    val builder = OkHttpClient.Builder()
      .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
      .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
      .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
      .retryOnConnectionFailure(false)
      .sslSocketFactory(
        SslUtils.sslSocketFactory,
        SslUtils.x509TrustManager
      )
      .hostnameVerifier(SslUtils.hostnameVerifier)
    interceptors.forEach {
      builder.addInterceptor(it)
    }
    if (isDebug) {
      builder.addInterceptor(HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
      })
    }
    return builder.build()
  }

}