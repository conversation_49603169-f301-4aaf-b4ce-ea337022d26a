package cn.cleartv.terminal.network

import cn.cleartv.terminal.log.Log
import cn.cleartv.terminal.network.model.TerminalHeartbeatRequest
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 心跳服务
 * 负责定时发送心跳保持与服务器的连接
 */
object HeartbeatService {

  private const val DEFAULT_HEARTBEAT_INTERVAL = 30_000L // 30秒
  private const val MIN_HEARTBEAT_INTERVAL = 10_000L // 最小10秒
  private const val MAX_HEARTBEAT_INTERVAL = 300_000L // 最大5分钟

  private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
  private var heartbeatJob: Job? = null
  private val isRunning = AtomicBoolean(false)

  // 心跳间隔（毫秒）
  private var heartbeatInterval = DEFAULT_HEARTBEAT_INTERVAL

  // 心跳状态
  private val _heartbeatState = MutableStateFlow<HeartbeatState>(HeartbeatState.Stopped)
  val heartbeatState: StateFlow<HeartbeatState> = _heartbeatState.asStateFlow()

  // 心跳统计
  private val _heartbeatStats = MutableStateFlow(HeartbeatStats())
  val heartbeatStats: StateFlow<HeartbeatStats> = _heartbeatStats.asStateFlow()

  /**
   * 心跳状态枚举
   */
  sealed class HeartbeatState {
    object Stopped : HeartbeatState()
    object Running : HeartbeatState()
    object Paused : HeartbeatState()
    data class Error(val message: String) : HeartbeatState()
  }

  /**
   * 心跳统计数据
   */
  data class HeartbeatStats(
    val totalSent: Long = 0,
    val successCount: Long = 0,
    val failureCount: Long = 0,
    val lastSuccessTime: Long = 0,
    val lastFailureTime: Long = 0,
    val consecutiveFailures: Int = 0
  ) {
    val successRate: Double
      get() = if (totalSent > 0) (successCount.toDouble() / totalSent) * 100 else 0.0
  }

  /**
   * 启动心跳服务
   */
  fun start() {
    if (isRunning.get()) {
      return
    }

    isRunning.set(true)
    _heartbeatState.value = HeartbeatState.Running

    heartbeatJob = scope.launch {
      Log.i("Starting heartbeat service...")
      while (isActive && isRunning.get()) {
        try {
          sendHeartbeat()
          delay(heartbeatInterval)
        } catch (e: CancellationException) {
          break
        } catch (e: Exception) {
          handleHeartbeatError(e)
          delay(heartbeatInterval)
        }
      }
    }
  }

  /**
   * 停止心跳服务
   */
  fun stop() {
    isRunning.set(false)
    heartbeatJob?.cancel()
    heartbeatJob = null
    _heartbeatState.value = HeartbeatState.Stopped
  }

  /**
   * 暂停心跳服务
   */
  fun pause() {
    if (isRunning.get()) {
      isRunning.set(false)
      _heartbeatState.value = HeartbeatState.Paused
    }
  }

  /**
   * 恢复心跳服务
   */
  fun resume() {
    if (_heartbeatState.value is HeartbeatState.Paused) {
      start()
    }
  }

  /**
   * 设置心跳间隔
   */
  fun setHeartbeatInterval(intervalMs: Long) {
    val validInterval = intervalMs.coerceIn(MIN_HEARTBEAT_INTERVAL, MAX_HEARTBEAT_INTERVAL)
    heartbeatInterval = validInterval
  }

  /**
   * 获取当前心跳间隔
   */
  fun getHeartbeatInterval(): Long = heartbeatInterval

  /**
   * 立即发送一次心跳
   */
  suspend fun sendImmediateHeartbeat(): Boolean {
    return try {
      sendHeartbeat()
      true
    } catch (e: Exception) {
      handleHeartbeatError(e)
      false
    }
  }

  /**
   * 发送心跳
   */
  private suspend fun sendHeartbeat() {
    val currentStats = _heartbeatStats.value

    // 创建心跳请求
    val heartbeatRequest = TerminalHeartbeatRequest()

    // 发送心跳
    val result = TerminalService.sendHeartbeat(heartbeatRequest)

    // 更新统计
    val newStats = if (result.isSuccess()) {
      currentStats.copy(
        totalSent = currentStats.totalSent + 1,
        successCount = currentStats.successCount + 1,
        lastSuccessTime = System.currentTimeMillis(),
        consecutiveFailures = 0
      )
    } else {
      currentStats.copy(
        totalSent = currentStats.totalSent + 1,
        failureCount = currentStats.failureCount + 1,
        lastFailureTime = System.currentTimeMillis(),
        consecutiveFailures = currentStats.consecutiveFailures + 1
      )
    }

    _heartbeatStats.value = newStats

    // 如果连续失败次数过多，调整心跳间隔
    if (newStats.consecutiveFailures >= 3) {
      adjustHeartbeatInterval(true)
    } else if (newStats.consecutiveFailures == 0 && heartbeatInterval > DEFAULT_HEARTBEAT_INTERVAL) {
      adjustHeartbeatInterval(false)
    }

    // 如果心跳失败，抛出异常
    if (result.isError()) {
      throw Exception(result.getErrorMessage())
    }
  }

  /**
   * 处理心跳错误
   */
  private fun handleHeartbeatError(error: Exception) {
    _heartbeatState.value = HeartbeatState.Error(error.message ?: "心跳发送失败")
    // 心跳会自动检查认证
//        // 如果是网络错误，可以考虑重新认证
//        if (error.message?.contains("401") == true || error.message?.contains("认证") == true) {
//            scope.launch {
//                terminalService.authenticate()
//            }
//        }
  }

  /**
   * 调整心跳间隔
   */
  private fun adjustHeartbeatInterval(increase: Boolean) {
    heartbeatInterval = if (increase) {
      // 增加间隔，最大不超过5分钟
      (heartbeatInterval * 1.5).toLong().coerceAtMost(MAX_HEARTBEAT_INTERVAL)
    } else {
      // 减少间隔，最小不低于默认值
      (heartbeatInterval * 0.8).toLong().coerceAtLeast(DEFAULT_HEARTBEAT_INTERVAL)
    }
  }

  /**
   * 重置统计数据
   */
  fun resetStats() {
    _heartbeatStats.value = HeartbeatStats()
  }

  /**
   * 检查服务是否正在运行
   */
  fun isRunning(): Boolean = isRunning.get()

  /**
   * 销毁服务
   */
  fun destroy() {
    stop()
    scope.cancel()
  }
}