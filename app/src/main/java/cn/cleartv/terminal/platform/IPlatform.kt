package cn.cleartv.terminal.platform

import android.annotation.SuppressLint
import cn.cleartv.terminal.ethernetManager
import cn.cleartv.terminal.log.Log
import cn.cleartv.terminal.utils.AppUtils
import cn.cleartv.terminal.utils.EthernetInfo
import cn.cleartv.terminal.utils.ShellUtil
import java.text.SimpleDateFormat

interface IPlatform {

  fun shutdown() {
    Log.i("shutdown")
    ShellUtil.execCommand("reboot -p", true)
  }

  fun reboot() {
    Log.i("reboot")
    ShellUtil.execCommand("reboot", true)
  }

  fun openNetworkAdb(open: <PERSON>ole<PERSON>, port: Int = 5555) {
    Log.i("openNetworkAdb: $open")
    val curPort =
      ShellUtil.execCommand("getprop service.adb.tcp.port", true).responseMsg?.toIntOrNull()
    if(curPort != port) ShellUtil.execCommand("setprop service.adb.tcp.port $port", true)
    val isOpen =
      "running" == ShellUtil.execCommand("getprop init.svc.adbd", true).responseMsg?.trim()
    if (isOpen && !open) {
      ShellUtil.execCommand("stop adbd", true)
    } else if (!isOpen && open) {
      ShellUtil.execCommand("start adbd", true)
    }
  }

  fun installApk(filePath: String) {
    Log.i("installApk: $filePath")
    if (!AppUtils.installAppSilent(filePath)) {
      AppUtils.installApp(filePath)
    }
  }

  fun uninstallApk(packageName: String) {
    Log.i("uninstallApk: $packageName")
    if (!AppUtils.uninstallAppSilent(packageName)) {
      AppUtils.uninstallApp(packageName)
    }
  }

  fun setEthernet(ethernetInfo: EthernetInfo) {
    Log.i("setEthernet: $ethernetInfo")
    try {
      ethernetInfo.apply {
        ethernetManager.setEthernet(
          useDhcp ?: false,
          ipAddress,
          gateWay,
          mask,
          dns1 ?: "*********",
          dns2 ?: "*******"
        )
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  fun getEthernetInfo(iface: String = "eth0"): EthernetInfo {
    Log.i("getEthernetInfo: $iface")
    val dnsList = ethernetManager.getDns(iface)
    return EthernetInfo(
      useDhcp = null,
      ipAddress = ethernetManager.getIpAddress(iface),
      gateWay = ethernetManager.getGateway(iface),
      mask = ethernetManager.getNetmask(iface),
      dns1 = dnsList.getOrNull(0),
      dns2 = dnsList.getOrNull(1),
    )
  }

  @SuppressLint("SimpleDateFormat")
  fun setTime(timeMillis: Long) {
    Log.i("setTime: $timeMillis")
    val time = SimpleDateFormat("MMddHHmmyyyy.ss").format(timeMillis)
    ShellUtil.execCommand("date $time set", true, true)
  }

  fun takeScreenshot(path: String) {
    Log.i("takeScreenshot: $path")
    ShellUtil.execCommand(arrayOf("screencap -p $path", "chmod 644 $path"), true, true)
  }

  fun replaceBootAnimation(path: String): Boolean {
    val commands = arrayOf(
      "mount -o rw,remount -t ext4 /system",
      "rm -rf system/media/bootanimation.zip",
      "cp  $path system/media/bootanimation.zip",
      "chmod 644 system/media/bootanimation.zip",
      "sync",
      "mount -o ro,remount -t ext4 /system"
    )

    if (ShellUtil.execCommand(commands, true, true).result < 0) return false
    reboot()
    return true
  }

}