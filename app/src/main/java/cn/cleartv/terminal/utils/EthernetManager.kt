package cn.cleartv.terminal.utils

import android.annotation.SuppressLint
import android.content.ContentResolver
import android.content.Context
import android.net.IpConfiguration
import android.net.LinkAddress
import android.net.StaticIpConfiguration
import android.provider.Settings
import java.net.InetAddress


@Suppress("UNCHECKED_CAST")
@SuppressLint("NewApi")
class EthernetManager(context: Context) {

  private val contentResolver: ContentResolver = context.contentResolver
  private val ethernetManager = context.getSystemService("ethernet")

  @SuppressLint("PrivateApi")
  private val ethernetManagerCls = Class.forName("android.net.EthernetManager")

  @SuppressLint("PrivateApi")
  private val ipAssignmentCls = Class.forName("android.net.IpConfiguration\$IpAssignment")

  @SuppressLint("PrivateApi")
  private val ipAssignmentEnums = ipAssignmentCls.enumConstants

  //  @RequiresApi(Build.VERSION_CODES.R)
  private fun getConfiguration(): IpConfiguration {
    return ethernetManagerCls.getMethod("getConfiguration")
      .invoke(ethernetManager) as IpConfiguration
  }

  private fun getConfiguration(iface: String?): IpConfiguration {
    return ethernetManagerCls.getMethod("getConfiguration", String::class.java)
      .invoke(ethernetManager, iface) as IpConfiguration
  }

  private fun setConfiguration(iface: String, configuration: IpConfiguration) {
    ethernetManagerCls.getMethod(
      "setConfiguration",
      String::class.java,
      IpConfiguration::class.java
    )
      .invoke(ethernetManager, iface, configuration)
  }

  private fun setConfiguration(configuration: IpConfiguration) {
    ethernetManagerCls.getMethod(
      "setConfiguration",
      IpConfiguration::class.java
    )
      .invoke(ethernetManager, configuration)
  }

  fun isAvailable(): Boolean {
    return ethernetManagerCls.getMethod("isAvailable")
      .invoke(ethernetManager) as Boolean
  }

  fun isAvailable(iface: String): Boolean {
    return ethernetManagerCls.getMethod("isAvailable", String::class.java)
      .invoke(ethernetManager, iface) as Boolean
  }

  fun getAvailableInterfaces(): Array<String> {
    return ethernetManagerCls.getMethod("getAvailableInterfaces")
      .invoke(ethernetManager) as Array<String>
  }

  fun setEthernetEnabled(enabled: Boolean) {
    ethernetManagerCls.getMethod("setEthernetEnabled", Boolean::class.java)
      .invoke(ethernetManager, enabled)
  }

  private fun getInterfaceList(): List<String> {
    return ethernetManagerCls.getMethod("getInterfaceList")
      .invoke(ethernetManager) as List<String>
  }

  fun getDns(iface: String): List<String> {
    return try {
      (ethernetManagerCls.getMethod("getDns", String::class.java)
        .invoke(ethernetManager, iface) as String).split(",").dropWhile { it.isBlank() }
    } catch (_: Exception) {
      NetworkUtils.getDns()
    }
  }

  fun getGateway(iface: String): String {
    return try {
      ethernetManagerCls.getMethod("getGateway", String::class.java)
        .invoke(ethernetManager, iface) as String
    } catch (_: Exception) {
      NetworkUtils.getGateWay(iface)
    }
  }

  fun getIpAddress(iface: String): String {
    return try {
      ethernetManagerCls.getMethod("getIpAddress", String::class.java)
        .invoke(ethernetManager, iface) as String
    } catch (_: Exception) {
      NetworkUtils.getIPAddress()
    }
  }

  fun getNetmask(iface: String): String {
    return try {
      return ethernetManagerCls.getMethod("getNetmask", String::class.java)
        .invoke(ethernetManager, iface) as String
    } catch (_: Exception) {
      NetworkUtils.getSubnetMask(iface)
    }
  }

  fun getEthernetIfaceState(iface: String): Int {
    return ethernetManagerCls.getMethod("getEthernetIfaceState", String::class.java)
      .invoke(ethernetManager, iface) as Int
  }

  fun setEthernet(
    dhcp: Boolean,
    ipAddress: String,
    gateway: String,
    subnetMask: String,
    dns1: String,
    dns2: String
  ) {
    if (dhcp) {
      setConfiguration(newIpConfiguration(IpAssignment.DHCP))
    } else {
      setConfiguration(
        newIpConfiguration(
          IpAssignment.STATIC,
          newStaticIpConfiguration(ipAddress, gateway, subnetMask, dns1, dns2)
        )
      )
    }

    saveEthernetSettings(dhcp, ipAddress, gateway, subnetMask, dns1, dns2)
  }

  fun setEthernet(
    iface: String,
    dhcp: Boolean,
    ipAddress: String,
    gateway: String,
    subnetMask: String,
    dns1: String,
    dns2: String
  ) {
    if (dhcp) {
      setConfiguration(iface, newIpConfiguration(IpAssignment.DHCP))
    } else {
      setConfiguration(
        iface,
        newIpConfiguration(
          IpAssignment.STATIC,
          newStaticIpConfiguration(ipAddress, gateway, subnetMask, dns1, dns2)
        )
      )
    }

    saveEthernetSettings(dhcp, ipAddress, gateway, subnetMask, dns1, dns2)
  }

  private fun saveEthernetSettings(
    dhcp: Boolean,
    ipAddress: String,
    gateway: String,
    subnetMask: String,
    dns1: String,
    dns2: String
  ) {
    Settings.Global.putInt(contentResolver, "ethernet_mode", if (dhcp) 0 else 1)
    Settings.Global.putString(contentResolver, "ethernet_static_ip", ipAddress)
    Settings.Global.putString(contentResolver, "ethernet_static_mask", subnetMask)
    Settings.Global.putString(contentResolver, "ethernet_static_gateway", gateway)
    Settings.Global.putString(contentResolver, "ethernet_static_dns1", dns1)
    Settings.Global.putString(contentResolver, "ethernet_static_dns2", dns2)
  }

  enum class IpAssignment {

    /* Use statically configured IP settings. Configuration can be accessed
         * with staticIpConfiguration */
    STATIC,

    /* Use dynamically configured IP settings */
    DHCP,

    /* no IP details are assigned, this is used to indicate
         * that any existing IP settings should be retained */
    UNASSIGNED
  }


  @SuppressLint("NewApi")
  private fun newStaticIpConfiguration(
    ipAddress: String,
    gateway: String,
    subnetMask: String,
    dns1: String,
    dns2: String
  ): StaticIpConfiguration {
    val linkAddress =
      LinkAddress::class.java.getConstructor(InetAddress::class.java, Int::class.java)
        .newInstance(
          InetAddress.getByName(ipAddress),
          NetworkUtils.subnetMaskToLength(subnetMask)
        ) as LinkAddress
    val staticIpConfiguration = StaticIpConfiguration.Builder()
      .setIpAddress(linkAddress)
      .setGateway(InetAddress.getByName(gateway))
      .setDnsServers(
        mutableListOf(
          InetAddress.getByName(dns1),
          InetAddress.getByName(dns2)
        )
      )
      .setDomains(subnetMask)
      .build()
    return staticIpConfiguration
  }

  @SuppressLint("NewApi")
  private fun newIpConfiguration(
    ipAssignment: EthernetManager.IpAssignment,
    staticIpConfiguration: StaticIpConfiguration? = null
  ): IpConfiguration {
    val ipAssignmentCls = Class.forName("android.net.IpConfiguration\$IpAssignment")
    val ipConfiguration =
      IpConfiguration::class.java.getConstructor().newInstance() as IpConfiguration
    IpConfiguration::class.java.getMethod(
      "setStaticIpConfiguration",
      StaticIpConfiguration::class.java
    )
      .invoke(ipConfiguration, staticIpConfiguration)
    IpConfiguration::class.java.getMethod(
      "setIpAssignment",
      ipAssignmentCls
    ).invoke(
      ipConfiguration,
      ipAssignmentCls.enumConstants.find { it.toString() == ipAssignment.toString() })
    return ipConfiguration
  }

}

/**
 * 网络配置
 *
 * @property useDhcp 使用DHCP，false时使用静态地址
 * @property ipAddress ip地址
 * @property gateWay 网关
 * @property mask 子网掩码
 * @property dns1 主DNS
 * @property dns2 副DNS
 */
data class EthernetInfo(
  val useDhcp: Boolean? = null,
  val ipAddress: String,
  val gateWay: String,
  val mask: String,
  val dns1: String? = "*********",
  val dns2: String? = "*******",
)