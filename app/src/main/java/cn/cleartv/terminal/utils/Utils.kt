package cn.cleartv.terminal.utils

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityManager
import android.app.Application
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Process
import android.text.TextUtils
import android.util.DisplayMetrics
import android.util.Log
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.Lifecycle
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.lang.reflect.Field
import java.lang.reflect.InvocationTargetException
import java.lang.reflect.Method
import java.util.LinkedList
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors


@Suppress("DEPRECATION")
@SuppressLint("ObsoleteSdkInt", "StaticFieldLeak")
object Utils {

  val UTIL_HANDLER: Handler = Handler(Looper.getMainLooper())
  private val ACTIVITY_LIFECYCLE = ActivityLifecycleImpl()
  private val UTIL_POOL: ExecutorService = Executors.newFixedThreadPool(3)

  private var sApplication: Application? = null

  /**
   * Init utils.
   *
   * Init it in the class of Application.
   *
   * @param context context
   */
  fun init(context: Context?) {
    if (context == null) {
      init(getApplicationByReflect())
      return
    }
    init(context.applicationContext as Application)
  }

  /**
   * Init utils.
   *
   * Init it in the class of Application.
   *
   * @param app application
   */
  fun init(app: Application) {
    if (sApplication == null) {
      sApplication = app
      app.registerActivityLifecycleCallbacks(ACTIVITY_LIFECYCLE)
    } else {
      if (app.javaClass != sApplication?.javaClass) {
        sApplication?.unregisterActivityLifecycleCallbacks(ACTIVITY_LIFECYCLE)
        ACTIVITY_LIFECYCLE.mActivityList.clear()
        sApplication = app
        app.registerActivityLifecycleCallbacks(ACTIVITY_LIFECYCLE)
      }
    }
  }

  /**
   * Return the context of Application object.
   *
   * @return the context of Application object
   */
  fun getApp(): Application {
    return sApplication ?: getApplicationByReflect().apply {
      init(this)
    }
  }

  fun getResources(): Resources {
    return getApp().resources
  }

  fun getActivityLifecycle(): ActivityLifecycleImpl {
    return ACTIVITY_LIFECYCLE
  }

  fun getActivityList(): LinkedList<Activity> {
    return ACTIVITY_LIFECYCLE.mActivityList
  }

  fun getTopActivity(): Activity? {
    return ACTIVITY_LIFECYCLE.topActivity
  }

  fun getTopActivityOrApp(): Context {
    return if (isAppForeground()) {
      ACTIVITY_LIFECYCLE.topActivity ?: getApp()
    } else {
      getApp()
    }
  }

  fun isAppForeground(): Boolean {
    val am: ActivityManager =
      getApp().getSystemService(Context.ACTIVITY_SERVICE) as? ActivityManager
        ?: return false
    val info = am.runningAppProcesses
    if (info == null || info.size == 0) return false
    for (aInfo in info) {
      if (aInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
        if (aInfo.processName == getApp().packageName) {
          return true
        }
      }
    }
    return false
  }

  fun <T> doAsync(task: Task<T>): Task<T> {
    UTIL_POOL.execute(task)
    return task
  }

  fun removeDelayedRunnable(runnable: Runnable) {
    UTIL_HANDLER.removeCallbacks(runnable)
  }

  fun runOnUiThread(runnable: Runnable) {
    if (Looper.myLooper() === Looper.getMainLooper()) {
      runnable.run()
    } else {
      UTIL_HANDLER.post(runnable)
    }
  }

  fun runOnUiThreadDelayed(runnable: Runnable, delayMillis: Long) {
    UTIL_HANDLER.postDelayed(runnable, delayMillis)
  }

  fun getCurrentProcessName(): String {
    var name = getCurrentProcessNameByFile()
    if (!TextUtils.isEmpty(name)) return name
    name = getCurrentProcessNameByAms()
    if (!TextUtils.isEmpty(name)) return name
    name = getCurrentProcessNameByReflect()
    return name
  }

  private fun getCurrentProcessNameByFile(): String {
    return try {
      val file = File("/proc/" + Process.myPid() + "/" + "cmdline")
      val mBufferedReader = BufferedReader(FileReader(file))
      val processName: String = mBufferedReader.readLine().trim()
      mBufferedReader.close()
      processName
    } catch (e: Exception) {
      e.printStackTrace()
      ""
    }
  }

  private fun getCurrentProcessNameByAms(): String {
    val am: ActivityManager =
      getApp().getSystemService(Context.ACTIVITY_SERVICE) as? ActivityManager
        ?: return ""
    val info = am.runningAppProcesses
    if (info == null || info.size == 0) return ""
    val pid = Process.myPid()
    for (aInfo in info) {
      if (aInfo.pid == pid) {
        if (aInfo.processName != null) {
          return aInfo.processName
        }
      }
    }
    return ""
  }

  private fun getCurrentProcessNameByReflect(): String {
    var processName = ""
    try {
      val app: Application = getApp()
      val loadedApkField: Field = app.javaClass.getField("mLoadedApk")
      loadedApkField.isAccessible = true
      val loadedApk = loadedApkField.get(app)
      val activityThreadField: Field = loadedApk.javaClass.getDeclaredField("mActivityThread")
      activityThreadField.isAccessible = true
      val activityThread = activityThreadField.get(loadedApk)
      val getProcessName: Method =
        activityThread.javaClass.getDeclaredMethod("getProcessName")
      processName = getProcessName.invoke(activityThread)?.toString() ?: ""
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return processName
  }

  private fun getApplicationByReflect(): Application {
    try {
      @SuppressLint("PrivateApi") val activityThread =
        Class.forName("android.app.ActivityThread")
      val thread = activityThread.getMethod("currentActivityThread").invoke(null)
      val app = activityThread.getMethod("getApplication").invoke(thread)
        ?: throw kotlin.NullPointerException("u should init first")
      return app as Application
    } catch (e: NoSuchMethodException) {
      e.printStackTrace()
    } catch (e: IllegalAccessException) {
      e.printStackTrace()
    } catch (e: InvocationTargetException) {
      e.printStackTrace()
    } catch (e: ClassNotFoundException) {
      e.printStackTrace()
    }
    throw kotlin.NullPointerException("u should init first")
  }

  /**
   * Set animators enabled.
   */
  private fun setAnimatorsEnabled() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && ValueAnimator.areAnimatorsEnabled()) {
      return
    }
    try {
      @SuppressLint("SoonBlockedPrivateApi")
      val sDurationScaleField: Field =
        ValueAnimator::class.java.getDeclaredField("sDurationScale")
      sDurationScaleField.isAccessible = true
      val sDurationScale = sDurationScaleField.get(null) as Float
      if (sDurationScale == 0f) {
        sDurationScaleField.set(null, 1f)
        Log.i("Utils", "setAnimatorsEnabled: Animators are enabled now!")
      }
    } catch (e: NoSuchFieldException) {
      e.printStackTrace()
    } catch (e: IllegalAccessException) {
      e.printStackTrace()
    }
  }

  fun addActivityLifecycleCallbacks(callbacks: ActivityLifecycleCallbacks?) {
    ACTIVITY_LIFECYCLE.addActivityLifecycleCallbacks(callbacks)
  }

  fun addActivityLifecycleCallbacks(activity: Activity?, callbacks: ActivityLifecycleCallbacks?) {
    ACTIVITY_LIFECYCLE.addActivityLifecycleCallbacks(activity, callbacks)
  }

  fun removeActivityLifecycleCallbacks(callbacks: ActivityLifecycleCallbacks?) {
    ACTIVITY_LIFECYCLE.removeActivityLifecycleCallbacks(callbacks)
  }

  fun removeActivityLifecycleCallbacks(activity: Activity?) {
    ACTIVITY_LIFECYCLE.removeActivityLifecycleCallbacks(activity)
  }

  fun removeActivityLifecycleCallbacks(
    activity: Activity?,
    callbacks: ActivityLifecycleCallbacks?
  ) {
    ACTIVITY_LIFECYCLE.removeActivityLifecycleCallbacks(activity, callbacks)
  }

  interface Callback<T> {
    fun onCall(data: T)
  }

  interface OnAppStatusChangedListener {
    fun onForeground()
    fun onBackground()
  }

  interface OnActivityDestroyedListener {
    fun onActivityDestroyed(activity: Activity?)
  }

  class ActivityLifecycleImpl : Application.ActivityLifecycleCallbacks {
    val mActivityList: LinkedList<Activity> = LinkedList()
    private val mStatusListenerMap: MutableMap<Any, OnAppStatusChangedListener> =
      kotlin.collections.HashMap()
    private val mDestroyedListenerMap: MutableMap<Activity, MutableSet<OnActivityDestroyedListener>> =
      kotlin.collections.HashMap()
    private var mForegroundCount = 0
    private var mConfigCount = 0
    private var mIsBackground = false
    private val mActivityLifecycleCallbacksMap: ConcurrentHashMap<Activity, MutableList<ActivityLifecycleCallbacks>> =
      ConcurrentHashMap()
    private val STUB = Activity()

    fun addActivityLifecycleCallbacks(listener: ActivityLifecycleCallbacks?) {
      addActivityLifecycleCallbacks(STUB, listener)
    }

    fun addActivityLifecycleCallbacks(
      activity: Activity?,
      listener: ActivityLifecycleCallbacks?
    ) {
      if (activity == null || listener == null) return
      runOnUiThread {
        addActivityLifecycleCallbacksInner(
          activity,
          listener
        )
      }
    }

    private fun addActivityLifecycleCallbacksInner(
      activity: Activity,
      callbacks: ActivityLifecycleCallbacks
    ) {
      var callbacksList: MutableList<ActivityLifecycleCallbacks>? =
        mActivityLifecycleCallbacksMap[activity]
      if (callbacksList == null) {
        callbacksList = CopyOnWriteArrayList()
        mActivityLifecycleCallbacksMap[activity] = callbacksList
      } else {
        if (callbacksList.contains(callbacks)) return
      }
      callbacksList.add(callbacks)
    }

    fun removeActivityLifecycleCallbacks(callbacks: ActivityLifecycleCallbacks?) {
      removeActivityLifecycleCallbacks(STUB, callbacks)
    }

    fun removeActivityLifecycleCallbacks(activity: Activity?) {
      if (activity == null) return
      runOnUiThread { mActivityLifecycleCallbacksMap.remove(activity) }
    }

    fun removeActivityLifecycleCallbacks(
      activity: Activity?,
      callbacks: ActivityLifecycleCallbacks?
    ) {
      if (activity == null || callbacks == null) return
      runOnUiThread {
        removeActivityLifecycleCallbacksInner(
          activity,
          callbacks
        )
      }
    }

    private fun removeActivityLifecycleCallbacksInner(
      activity: Activity,
      callbacks: ActivityLifecycleCallbacks
    ) {
      val callbacksList = mActivityLifecycleCallbacksMap[activity]
      if (!callbacksList.isNullOrEmpty()) {
        callbacksList.remove(callbacks)
      }
    }

    private fun consumeActivityLifecycleCallbacks(activity: Activity, event: Lifecycle.Event) {
      consumeLifecycle(activity, event, mActivityLifecycleCallbacksMap[activity])
      consumeLifecycle(activity, event, mActivityLifecycleCallbacksMap[STUB])
    }

    private fun consumeLifecycle(
      activity: Activity,
      event: Lifecycle.Event,
      listeners: List<ActivityLifecycleCallbacks>?
    ) {
      if (listeners == null) return
      for (listener in listeners) {
        listener.onLifecycleChanged(activity, event)
        when (event) {
          Lifecycle.Event.ON_CREATE -> {
            listener.onActivityCreated(activity)
          }

          Lifecycle.Event.ON_START -> {
            listener.onActivityStarted(activity)
          }

          Lifecycle.Event.ON_RESUME -> {
            listener.onActivityResumed(activity)
          }

          Lifecycle.Event.ON_PAUSE -> {
            listener.onActivityPaused(activity)
          }

          Lifecycle.Event.ON_STOP -> {
            listener.onActivityStopped(activity)
          }

          Lifecycle.Event.ON_DESTROY -> {
            listener.onActivityDestroyed(activity)
          }

          Lifecycle.Event.ON_ANY -> {
          }
        }
      }
      if (event == Lifecycle.Event.ON_DESTROY) {
        mActivityLifecycleCallbacksMap.remove(activity)
      }
    }


    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
      setAnimatorsEnabled()
      topActivity = activity
      consumeActivityLifecycleCallbacks(activity, Lifecycle.Event.ON_CREATE)
    }

    override fun onActivityStarted(activity: Activity) {
      if (!mIsBackground) {
        topActivity = activity
      }
      if (mConfigCount < 0) {
        ++mConfigCount
        updateAppConfig(activity)
      } else {
        ++mForegroundCount
      }
      consumeActivityLifecycleCallbacks(activity, Lifecycle.Event.ON_START)
    }

    override fun onActivityResumed(activity: Activity) {
      topActivity = activity
      if (mIsBackground) {
        mIsBackground = false
        postStatus(true)
      }
      consumeActivityLifecycleCallbacks(activity, Lifecycle.Event.ON_RESUME)
    }

    override fun onActivityPaused(activity: Activity) {
      consumeActivityLifecycleCallbacks(activity, Lifecycle.Event.ON_PAUSE)
    }

    override fun onActivityStopped(activity: Activity) {
      if (activity.isChangingConfigurations) {
        --mConfigCount
      } else {
        --mForegroundCount
        if (mForegroundCount <= 0) {
          mIsBackground = true
          postStatus(false)
        }
      }
      consumeActivityLifecycleCallbacks(activity, Lifecycle.Event.ON_STOP)
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    }


    override fun onActivityDestroyed(activity: Activity) {
      mActivityList.remove(activity)
      consumeOnActivityDestroyedListener(activity)
      fixSoftInputLeaks(activity)
      consumeActivityLifecycleCallbacks(activity, Lifecycle.Event.ON_DESTROY)
    }

    var topActivity: Activity?
      get() {
        if (!mActivityList.isEmpty()) {
          for (i in mActivityList.size - 1 downTo 0) {
            val activity: Activity = mActivityList[i]
            if (activity.isFinishing
              || Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && activity.isDestroyed
            ) {
              continue
            }
            return activity
          }
        }
        val topActivityByReflect: Activity? = topActivityByReflect
        if (topActivityByReflect != null) {
          topActivity = topActivityByReflect
        }
        return topActivityByReflect
      }
      private set(activity) {
        if (activity == null) return
        if (mActivityList.contains(activity)) {
          if (mActivityList.last != activity) {
            mActivityList.remove(activity)
            mActivityList.addLast(activity)
          }
        } else {
          mActivityList.addLast(activity)
        }
      }

    fun addOnAppStatusChangedListener(
      any: Any,
      listener: OnAppStatusChangedListener
    ) {
      mStatusListenerMap[any] = listener
    }

    fun removeOnAppStatusChangedListener(any: Any) {
      mStatusListenerMap.remove(any)
    }

    fun removeOnActivityDestroyedListener(activity: Activity?) {
      if (activity == null) return
      mDestroyedListenerMap.remove(activity)
    }

    fun addOnActivityDestroyedListener(
      activity: Activity?,
      listener: OnActivityDestroyedListener?
    ) {
      if (activity == null || listener == null) return
      val listeners: MutableSet<OnActivityDestroyedListener>
      if (!mDestroyedListenerMap.containsKey(activity)) {
        listeners = kotlin.collections.HashSet()
        mDestroyedListenerMap[activity] = listeners
      } else {
        listeners = mDestroyedListenerMap[activity]!!
        if (listeners.contains(listener)) return
      }
      listeners.add(listener)
    }

    private fun updateAppConfig(activity: Activity) {
      val resources: Resources = getApp().resources
      val dm: DisplayMetrics = resources.displayMetrics
      val config: Configuration = resources.configuration
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        config.setLocales(activity.resources.configuration.locales)
      } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
        config.setLocale(activity.resources.configuration.locale)
      }
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
        getApp().createConfigurationContext(config)
      } else {
        resources.updateConfiguration(config, dm)
      }
    }

    private fun postStatus(isForeground: Boolean) {
      if (mStatusListenerMap.isEmpty()) return
      for (onAppStatusChangedListener in mStatusListenerMap.values) {
        if (isForeground) {
          onAppStatusChangedListener.onForeground()
        } else {
          onAppStatusChangedListener.onBackground()
        }
      }
    }

    private fun consumeOnActivityDestroyedListener(activity: Activity) {
      val iterator =
        mDestroyedListenerMap.entries.iterator()
      while (iterator.hasNext()) {
        val entry = iterator.next()
        if (entry.key === activity) {
          val value = entry.value
          for (listener in value) {
            listener.onActivityDestroyed(activity)
          }
          iterator.remove()
        }
      }
    }

    private val topActivityByReflect: Activity?
      get() {
        try {
          @SuppressLint("PrivateApi") val activityThreadClass =
            Class.forName("android.app.ActivityThread")
          val currentActivityThreadMethod =
            activityThreadClass.getMethod("currentActivityThread").invoke(null)
          val mActivityListField: Field =
            activityThreadClass.getDeclaredField("mActivityList")
          mActivityListField.isAccessible = true
          val activities =
            mActivityListField.get(currentActivityThreadMethod) as? Map<*, *>
              ?: return null
          for (activityRecord in activities.values) {
            if (activityRecord == null) return null
            val activityRecordClass = activityRecord.javaClass
            val pausedField: Field = activityRecordClass.getDeclaredField("paused")
            pausedField.isAccessible = true
            if (!pausedField.getBoolean(activityRecord)) {
              val activityField: Field =
                activityRecordClass.getDeclaredField("activity")
              activityField.isAccessible = true
              return activityField.get(activityRecord) as Activity
            }
          }
        } catch (e: ClassNotFoundException) {
          e.printStackTrace()
        } catch (e: IllegalAccessException) {
          e.printStackTrace()
        } catch (e: InvocationTargetException) {
          e.printStackTrace()
        } catch (e: NoSuchMethodException) {
          e.printStackTrace()
        } catch (e: NoSuchFieldException) {
          e.printStackTrace()
        }
        return null
      }

    companion object {
      private fun fixSoftInputLeaks(activity: Activity?) {
        if (activity == null) return
        val imm: InputMethodManager =
          getApp().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            ?: return
        val leakViews =
          arrayOf("mLastSrvView", "mCurRootView", "mServedView", "mNextServedView")
        for (leakView in leakViews) {
          try {
            val leakViewField: Field =
              InputMethodManager::class.java.getDeclaredField(leakView) ?: continue
            if (!leakViewField.isAccessible) {
              leakViewField.isAccessible = true
            }
            val obj: Any = leakViewField.get(imm) as? View ?: continue
            val view: View = obj as View
            if (view.rootView === activity.window.decorView.rootView) {
              leakViewField.set(imm, null)
            }
          } catch (ignore: Throwable) { /**/
          }
        }
      }
    }
  }

  class FileProvider : androidx.core.content.FileProvider() {
    override fun onCreate(): Boolean {
      init(context)
      return true
    }
  }

  abstract class Task<Result>(private val mCallback: Callback<Result>) : Runnable {
    @Volatile
    private var state = NEW
    abstract fun doInBackground(): Result
    override fun run() {
      try {
        val t = doInBackground()
        if (state != NEW) return
        state = COMPLETING
        UTIL_HANDLER.post { mCallback.onCall(t) }
      } catch (th: Throwable) {
        if (state != NEW) return
        state = EXCEPTIONAL
      }
    }

    fun cancel() {
      state = CANCELLED
    }

    val isDone: Boolean
      get() = state != NEW

    val isCanceled: Boolean
      get() = state == CANCELLED

    companion object {
      private const val NEW = 0
      private const val COMPLETING = 1
      private const val CANCELLED = 2
      private const val EXCEPTIONAL = 3
    }
  }

  open class ActivityLifecycleCallbacks {
    open fun onActivityCreated(activity: Activity) { /**/
    }

    fun onActivityStarted(activity: Activity) { /**/
    }

    fun onActivityResumed(activity: Activity) { /**/
    }

    fun onActivityPaused(activity: Activity) { /**/
    }

    fun onActivityStopped(activity: Activity) { /**/
    }

    fun onActivityDestroyed(activity: Activity) { /**/
    }

    fun onLifecycleChanged(activity: Activity, event: Lifecycle.Event) { /**/
    }
  }

}