package cn.cleartv.terminal.utils

import android.Manifest
import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.net.wifi.ScanResult
import android.net.wifi.WifiManager
import android.os.Build
import android.provider.Settings
import android.telephony.TelephonyManager
import android.text.TextUtils
import android.text.format.Formatter
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.annotation.RequiresPermission
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.*
import java.util.*
import kotlin.text.equals

/**
 * <pre>
 *     author : Lee
 *     e-mail : <EMAIL>
 *     time   : 2021/04/07
 *     desc   :
 *     version: 1.0
 * </pre>
 */
object NetworkUtils {
  enum class NetworkType {
    NETWORK_ETHERNET, NETWORK_WIFI, NETWORK_5G, NETWORK_4G, NETWORK_3G, NETWORK_2G, NETWORK_UNKNOWN, NETWORK_NO
  }

  val app: Context get() = Utils.getApp()

  /**
   * Open the settings of wireless.
   */
  fun openWirelessSettings() {
    app.startActivity(
      Intent(Settings.ACTION_WIRELESS_SETTINGS)
        .setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    )
  }

  /**
   * Return whether network is connected.
   *
   * Must hold `<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />`
   *
   * @return `true`: connected<br></br>`false`: disconnected
   */
  @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
  fun isConnected(): Boolean {
    val info = getActiveNetworkInfo()
    return info != null && info.isConnected
  }

  /**
   * Return whether network is available.
   *
   * Must hold `<uses-permission android:name="android.permission.INTERNET" />`
   *
   * @return `true`: yes<br></br>`false`: no
   */
  @RequiresPermission(Manifest.permission.INTERNET)
  fun isAvailable(): Boolean {
    return isAvailableByDns() || isAvailableByPing()
  }

  /**
   * Return whether network is available using ping.
   *
   * Must hold `<uses-permission android:name="android.permission.INTERNET" />`
   *
   * @param ip The ip address.
   * @return `true`: yes<br></br>`false`: no
   */
  @RequiresPermission(Manifest.permission.INTERNET)
  fun isAvailableByPing(ip: String? = null): Boolean {
    val realIp = if (TextUtils.isEmpty(ip)) "*********" else ip
    val result = ShellUtil.execCommand(String.format("ping -c 1 %s", realIp), false)
    return result.result == 0
  }

  /**
   * Return whether network is available using domain.
   *
   * Must hold `<uses-permission android:name="android.permission.INTERNET" />`
   *
   * @param domain The name of domain.
   * @return `true`: yes<br></br>`false`: no
   */
  @RequiresPermission(Manifest.permission.INTERNET)
  fun isAvailableByDns(domain: String? = null): Boolean {
    val realDomain = if (TextUtils.isEmpty(domain)) "www.baidu.com" else domain
    val inetAddress: InetAddress?
    return try {
      inetAddress = InetAddress.getByName(realDomain)
      inetAddress != null
    } catch (e: UnknownHostException) {
      e.printStackTrace()
      false
    }
  }

  /**
   * Return whether mobile data is enabled.
   *
   * @return `true`: enabled<br></br>`false`: disabled
   */
  @SuppressLint("MissingPermission")
  fun getMobileDataEnabled(): Boolean {
    try {
      val tm = app.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
        ?: return false
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
        return tm.isDataEnabled
      }
      @SuppressLint("PrivateApi") val getMobileDataEnabledMethod =
        tm.javaClass.getDeclaredMethod("getDataEnabled")
      return getMobileDataEnabledMethod.invoke(tm) as Boolean
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return false
  }

  /**
   * Return whether using mobile data.
   *
   * Must hold `<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />`
   *
   * @return `true`: yes<br></br>`false`: no
   */
  @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
  fun isMobileData(): Boolean {
    val info = getActiveNetworkInfo()
    return (null != info && info.isAvailable
        && info.type == ConnectivityManager.TYPE_MOBILE)
  }

  /**
   * Return whether using 4G.
   *
   * Must hold `<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />`
   *
   * @return `true`: yes<br></br>`false`: no
   */
  @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
  fun is4G(): Boolean {
    val info = getActiveNetworkInfo()
    return (info != null && info.isAvailable
        && info.subtype == TelephonyManager.NETWORK_TYPE_LTE)
  }

  /**
   * Return whether using 4G.
   *
   * Must hold `<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />`
   *
   * @return `true`: yes<br></br>`false`: no
   */
  @RequiresApi(Build.VERSION_CODES.Q)
  @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
  fun is5G(): Boolean {
    val info = getActiveNetworkInfo()
    return (info != null && info.isAvailable
        && info.subtype == TelephonyManager.NETWORK_TYPE_NR)
  }

  /**
   * Return whether wifi is enabled.
   *
   * Must hold `<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />`
   *
   * @return `true`: enabled<br></br>`false`: disabled
   */
  @RequiresPermission(Manifest.permission.ACCESS_WIFI_STATE)
  fun getWifiEnabled(): Boolean {
    @SuppressLint("WifiManagerLeak") val manager =
      app.getSystemService(Context.WIFI_SERVICE) as? WifiManager
        ?: return false
    return manager.isWifiEnabled
  }

  /**
   * Enable or disable wifi.
   *
   * Must hold `<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />`
   *
   * @param enabled True to enabled, false otherwise.
   */
  @RequiresPermission(Manifest.permission.CHANGE_WIFI_STATE)
  fun setWifiEnabled(enabled: Boolean) {
    @SuppressLint("WifiManagerLeak") val manager =
      app.getSystemService(Context.WIFI_SERVICE) as? WifiManager
        ?: return
    if (enabled == manager.isWifiEnabled) return
    manager.isWifiEnabled = enabled
  }

  /**
   * Return whether wifi is connected.
   *
   * Must hold `<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />`
   *
   * @return `true`: connected<br></br>`false`: disconnected
   */
  @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
  fun isWifiConnected(): Boolean {
    val cm = app.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
      ?: return false
    val ni = cm.activeNetworkInfo
    return ni != null && ni.type == ConnectivityManager.TYPE_WIFI
  }

  /**
   * Return whether wifi is available.
   *
   * Must hold `<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />`,
   * `<uses-permission android:name="android.permission.INTERNET" />`
   *
   * @return `true`: available<br></br>`false`: unavailable
   */
  @RequiresPermission(allOf = [Manifest.permission.ACCESS_WIFI_STATE, Manifest.permission.INTERNET])
  fun isWifiAvailable(): Boolean {
    return getWifiEnabled() && isAvailable()
  }

  /**
   * Return the name of network operate.
   *
   * @return the name of network operate
   */
  fun getNetworkOperatorName(): String {
    val tm = app.getSystemService(Context.TELEPHONY_SERVICE) as? TelephonyManager
      ?: return ""
    return tm.networkOperatorName
  }

  /**
   * Return type of network.
   *
   * Must hold `<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />`
   *
   * @return type of network
   *
   *  * [NetworkUtils.NetworkType.NETWORK_ETHERNET]
   *  * [NetworkUtils.NetworkType.NETWORK_WIFI]
   *  * [NetworkUtils.NetworkType.NETWORK_4G]
   *  * [NetworkUtils.NetworkType.NETWORK_3G]
   *  * [NetworkUtils.NetworkType.NETWORK_2G]
   *  * [NetworkUtils.NetworkType.NETWORK_UNKNOWN]
   *  * [NetworkUtils.NetworkType.NETWORK_NO]
   *
   */
  @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
  fun getNetworkType(): NetworkType {
    if (isEthernet()) {
      return NetworkType.NETWORK_ETHERNET
    }
    val info = getActiveNetworkInfo()
    return if (info != null && info.isAvailable) {
      if (info.type == ConnectivityManager.TYPE_WIFI) {
        NetworkType.NETWORK_WIFI
      } else if (info.type == ConnectivityManager.TYPE_MOBILE) {
        when (info.subtype) {
          TelephonyManager.NETWORK_TYPE_GSM, TelephonyManager.NETWORK_TYPE_GPRS, TelephonyManager.NETWORK_TYPE_CDMA, TelephonyManager.NETWORK_TYPE_EDGE, TelephonyManager.NETWORK_TYPE_1xRTT, TelephonyManager.NETWORK_TYPE_IDEN -> NetworkType.NETWORK_2G
          TelephonyManager.NETWORK_TYPE_TD_SCDMA, TelephonyManager.NETWORK_TYPE_EVDO_A, TelephonyManager.NETWORK_TYPE_UMTS, TelephonyManager.NETWORK_TYPE_EVDO_0, TelephonyManager.NETWORK_TYPE_HSDPA, TelephonyManager.NETWORK_TYPE_HSUPA, TelephonyManager.NETWORK_TYPE_HSPA, TelephonyManager.NETWORK_TYPE_EVDO_B, TelephonyManager.NETWORK_TYPE_EHRPD, TelephonyManager.NETWORK_TYPE_HSPAP -> NetworkType.NETWORK_3G
          TelephonyManager.NETWORK_TYPE_IWLAN, TelephonyManager.NETWORK_TYPE_LTE -> NetworkType.NETWORK_4G
          TelephonyManager.NETWORK_TYPE_NR -> NetworkType.NETWORK_5G
          else -> {
            val subtypeName = info.subtypeName
            if (subtypeName.equals("TD-SCDMA", ignoreCase = true)
              || subtypeName.equals("WCDMA", ignoreCase = true)
              || subtypeName.equals("CDMA2000", ignoreCase = true)
            ) {
              NetworkType.NETWORK_3G
            } else {
              NetworkType.NETWORK_UNKNOWN
            }
          }
        }
      } else {
        NetworkType.NETWORK_UNKNOWN
      }
    } else NetworkType.NETWORK_NO
  }

  /**
   * Return whether using ethernet.
   *
   * Must hold
   * `<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />`
   *
   * @return `true`: yes<br></br>`false`: no
   */
  @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
  private fun isEthernet(): Boolean {
    val cm = app.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
      ?: return false
    val info = cm.getNetworkInfo(ConnectivityManager.TYPE_ETHERNET) ?: return false
    val state = info.state ?: return false
    return state == NetworkInfo.State.CONNECTED || state == NetworkInfo.State.CONNECTING
  }

  @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
  private fun getActiveNetworkInfo(): NetworkInfo? {
    val cm = app.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
      ?: return null
    return cm.activeNetworkInfo
  }

  /**
   * Return the ip address.
   *
   * Must hold `<uses-permission android:name="android.permission.INTERNET" />`
   *
   * @param useIPv4 True to use ipv4, false otherwise.
   * @return the ip address
   */
  @RequiresPermission(Manifest.permission.INTERNET)
  fun getIPAddress(useIPv4: Boolean = true): String {
    try {
      val nis = NetworkInterface.getNetworkInterfaces()
      val adds = LinkedList<InetAddress>()
      while (nis.hasMoreElements()) {
        val ni = nis.nextElement()
        // To prevent phone of xiaomi return "*********"
        if (!ni.isUp || ni.isLoopback) continue
        val addresses = ni.inetAddresses
        while (addresses.hasMoreElements()) {
          adds.addFirst(addresses.nextElement())
        }
      }
      for (add in adds) {
        if (!add.isLoopbackAddress) {
          val hostAddress = add.hostAddress
          val isIPv4 = hostAddress.indexOf(':') < 0
          if (useIPv4) {
            if (isIPv4) return hostAddress
          } else {
            if (!isIPv4) {
              val index = hostAddress.indexOf('%')
              return if (index < 0) hostAddress.uppercase() else hostAddress.substring(
                0,
                index
              ).uppercase()
            }
          }
        }
      }
    } catch (e: SocketException) {
      e.printStackTrace()
    }
    return ""
  }

  /**
   * Return the ip address of broadcast.
   *
   * @return the ip address of broadcast
   */
  fun getBroadcastIpAddress(): String {
    try {
      val nis = NetworkInterface.getNetworkInterfaces()
      while (nis.hasMoreElements()) {
        val ni = nis.nextElement()
        if (!ni.isUp || ni.isLoopback) continue
        val ias = ni.interfaceAddresses
        var i = 0
        val size = ias.size
        while (i < size) {
          val ia = ias[i]
          val broadcast = ia.broadcast
          if (broadcast != null) {
            return broadcast.hostAddress
          }
          i++
        }
      }
    } catch (e: SocketException) {
      e.printStackTrace()
    }
    return ""
  }

  /**
   * Return the domain address.
   *
   * Must hold `<uses-permission android:name="android.permission.INTERNET" />`
   *
   * @param domain The name of domain.
   * @return the domain address
   */
  @RequiresPermission(Manifest.permission.INTERNET)
  fun getDomainAddress(domain: String?): String {
    return try {
      InetAddress.getByName(domain).hostAddress
    } catch (e: UnknownHostException) {
      e.printStackTrace()
      ""
    }
  }

  /**
   * Return the ip address by wifi.
   *
   * @return the ip address by wifi
   */
  @RequiresPermission(Manifest.permission.ACCESS_WIFI_STATE)
  fun getIpAddressByWifi(): String {
    @SuppressLint("WifiManagerLeak") val wm =
      app.getSystemService(Context.WIFI_SERVICE) as? WifiManager
        ?: return ""
    return Formatter.formatIpAddress(wm.dhcpInfo.ipAddress)
  }

  /**
   * Return the gate way by wifi.
   *
   * @return the gate way by wifi
   */
  @RequiresPermission(Manifest.permission.ACCESS_WIFI_STATE)
  fun getGatewayByWifi(): String {
    @SuppressLint("WifiManagerLeak") val wm =
      app.getSystemService(Context.WIFI_SERVICE) as? WifiManager
        ?: return ""
    return Formatter.formatIpAddress(wm.dhcpInfo.gateway)
  }

  /**
   * Return the net mask by wifi.
   *
   * @return the net mask by wifi
   */
  @RequiresPermission(Manifest.permission.ACCESS_WIFI_STATE)
  fun getNetMaskByWifi(): String {
    @SuppressLint("WifiManagerLeak") val wm =
      app.getSystemService(Context.WIFI_SERVICE) as? WifiManager
        ?: return ""
    return Formatter.formatIpAddress(wm.dhcpInfo.netmask)
  }

  /**
   * Return the server address by wifi.
   *
   * @return the server address by wifi
   */
  @RequiresPermission(Manifest.permission.ACCESS_WIFI_STATE)
  fun getServerAddressByWifi(): String {
    @SuppressLint("WifiManagerLeak") val wm =
      app.getSystemService(Context.WIFI_SERVICE) as? WifiManager
        ?: return ""
    return Formatter.formatIpAddress(wm.dhcpInfo.serverAddress)
  }

  /**
   * Return the ssid.
   *
   * @return the ssid.
   */
  @RequiresPermission(Manifest.permission.ACCESS_WIFI_STATE)
  fun getSSID(): String {
    val wm = app.applicationContext.getSystemService(Context.WIFI_SERVICE) as? WifiManager
      ?: return ""
    val wi = wm.connectionInfo ?: return ""
    val ssid = wi.ssid
    if (TextUtils.isEmpty(ssid)) {
      return ""
    }
    return if (ssid.length > 2 && ssid[0] == '"' && ssid[ssid.length - 1] == '"') {
      ssid.substring(1, ssid.length - 1)
    } else ssid
  }

  /**
   * Register the status of network changed listener.
   *
   * @param listener The status of network changed listener
   */
  @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
  fun registerNetworkStatusChangedListener(listener: OnNetworkStatusChangedListener?) {
    NetworkChangedReceiver.instance.registerListener(listener)
  }

  /**
   * Return whether the status of network changed listener has been registered.
   *
   * @param listener The listener
   * @return true to registered, false otherwise.
   */
  fun isRegisteredNetworkStatusChangedListener(listener: OnNetworkStatusChangedListener?): Boolean {
    return NetworkChangedReceiver.instance.isRegistered(listener)
  }

  /**
   * Unregister the status of network changed listener.
   *
   * @param listener The status of network changed listener.
   */
  fun unregisterNetworkStatusChangedListener(listener: OnNetworkStatusChangedListener?) {
    NetworkChangedReceiver.instance.unregisterListener(listener)
  }

//    @RequiresPermission(allOf = [Manifest.permission.ACCESS_WIFI_STATE, Manifest.permission.ACCESS_COARSE_LOCATION])
//    fun getWifiScanResult(): WifiScanResults? {
//        val result = WifiScanResults()
//        if (!getWifiEnabled()) return result
//        @SuppressLint("WifiManagerLeak") val wm =
//            app.getSystemService(Context.WIFI_SERVICE) as? WifiManager
//        val scanResults = wm?.scanResults
//        if (scanResults != null) {
//            result.allResults = scanResults
//        }
//        return result
//    }

  fun getSubnetMask(interfaceName: String = "eth0"): String {
    try {
      //获取本机所有的网络接口
      val networkInterfaceEnumeration = NetworkInterface.getNetworkInterfaces()
      //判断 Enumeration 对象中是否还有数据
      while (networkInterfaceEnumeration.hasMoreElements()) {

        //获取 Enumeration 对象中的下一个数据
        val networkInterface = networkInterfaceEnumeration.nextElement()
        if (!networkInterface.isUp) {
          // 判断网口是否在使用
          continue
        }
        if (interfaceName != networkInterface.displayName) {
          // 判断是否时我们获取的网口
          continue
        }
        for (interfaceAddress in networkInterface.interfaceAddresses) {
          if (interfaceAddress.address is Inet4Address) {

            //仅仅处理ipv4
            //获取掩码位数，通过 calcMaskByPrefixLength 转换为字符串
            return calcMaskByPrefixLength(interfaceAddress.networkPrefixLength.toInt())
          }
        }
      }
    } catch (e: SocketException) {
      e.printStackTrace()
    }
    return "0.0.0.0"
  }

  //通过子网掩码的位数计算子网掩码
  private fun calcMaskByPrefixLength(length: Int): String {
    val mask = -0x1 shl 32 - length
    val partsNum = 4
    val bitsOfPart = 8
    val maskParts = IntArray(partsNum)
    val selector = 0x000000ff
    for (i in maskParts.indices) {
      val pos = maskParts.size - 1 - i
      maskParts[pos] = mask shr i * bitsOfPart and selector
    }
    var result = ""
    result += maskParts[0]
    for (i in 1 until maskParts.size) {
      result = result + "." + maskParts[i]
    }
    return result
  }

  fun subnetMaskToLength(subnetMask: String): Int {
    // 将子网掩码字符串按 . 分割成 4 个部分
    val parts = subnetMask.split(".")
    var length = 0
    for (part in parts) {
      // 将每个部分转换为整数
      val num = part.toInt()
      // 将整数转换为 8 位二进制字符串
      val binary = String.format("%8s", Integer.toBinaryString(num)).replace(' ', '0')
      // 统计二进制字符串中 1 的个数
      length += binary.count { it == '1' }
    }
    return length
  }

  fun getGateWay(interfaceName: String): String {
    var arr: Array<String> = arrayOf()
    try {
      val process = Runtime.getRuntime().exec("ip route list table 0")
      val reader = BufferedReader(InputStreamReader(process.inputStream))
      do {

        val string = reader.readLine()
        string ?: break

        if (string.indexOf(interfaceName) != -1) {
          arr = string.split("\\s+".toRegex()).toTypedArray()
          break
        }
      } while (!string.isNullOrBlank())

      return arr[2]
    } catch (e: Exception) {
      Log.e("NetworkUtils", "getGateWay($interfaceName) error, ${e.message}")
    }
    return "0.0.0.0"
  }

  fun getDns(): List<String> {
    var dnsList: MutableList<String> = mutableListOf()
    try {
      val process = Runtime.getRuntime().exec("getprop | grep net.dns")
      val reader = BufferedReader(InputStreamReader(process.inputStream))
      do {

        val string = reader.readLine()
        string ?: break
        dnsList.add(string.split(":")[1].trim().removeSurrounding("[", "]"))
      } while (string.isNotBlank())

      return dnsList
    } catch (e: Exception) {
      Log.e("NetworkUtils", "getDns() error, ${e.message}")
    }
    return dnsList
  }

  class NetworkChangedReceiver : BroadcastReceiver() {
    private var mType: NetworkType? = null
    private val mListeners: MutableSet<OnNetworkStatusChangedListener> = HashSet()

    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    fun registerListener(listener: OnNetworkStatusChangedListener?) {
      if (listener == null) return

      Utils.runOnUiThread {
        val preSize = mListeners.size
        mListeners.add(listener)
        if (preSize == 0 && mListeners.size == 1) {
          mType = getNetworkType()
          val intentFilter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
          app.registerReceiver(instance, intentFilter)
        }
      }
    }

    fun isRegistered(listener: OnNetworkStatusChangedListener?): Boolean {
      return if (listener == null) false else mListeners.contains(listener)
    }

    fun unregisterListener(listener: OnNetworkStatusChangedListener?) {
      if (listener == null) return
      Utils.runOnUiThread {
        val preSize = mListeners.size
        mListeners.remove(listener)
        if (preSize == 1 && mListeners.isEmpty()) {
          app.unregisterReceiver(instance)
        }
      }
    }

    @SuppressLint("MissingPermission")
    val networkChanged = Runnable {
      val networkType: NetworkType = getNetworkType()
      if (mType != networkType) {
        mType = networkType
        if (networkType == NetworkType.NETWORK_NO) {
          for (listener in mListeners) {
            listener.onDisconnected()
          }
        } else {
          for (listener in mListeners) {
            listener.onConnected(networkType)
          }
        }
      }
    }

    override fun onReceive(context: Context, intent: Intent) {
      if (ConnectivityManager.CONNECTIVITY_ACTION == intent.action) {
        // debouncing
        Utils.removeDelayedRunnable(networkChanged)
        Utils.runOnUiThreadDelayed(networkChanged, 1000)
      }
    }

    companion object {
      val instance: NetworkChangedReceiver by lazy {
        NetworkChangedReceiver()
      }
    }
  }

  interface OnNetworkStatusChangedListener {
    fun onDisconnected()
    fun onConnected(networkType: NetworkType)
  }

  class WifiScanResults {
    var allResults: List<ScanResult> = ArrayList()
      set(allResults) {
        field = allResults
        filterResults = filterScanResult(allResults)
      }
    var filterResults: List<ScanResult> = ArrayList()
      private set

    companion object {
      private fun filterScanResult(results: List<ScanResult>?): List<ScanResult> {
        if (results == null || results.isEmpty()) {
          return ArrayList()
        }
        val map = LinkedHashMap<String, ScanResult>(results.size)
        for (result in results) {
          if (TextUtils.isEmpty(result.SSID)) {
            continue
          }
          val resultInMap = map[result.SSID]
          if (resultInMap != null && resultInMap.level >= result.level) {
            continue
          }
          map[result.SSID] = result
        }
        return ArrayList(map.values)
      }
    }
  }
}
