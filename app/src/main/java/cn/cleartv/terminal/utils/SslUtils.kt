package cn.cleartv.terminal.utils

import android.annotation.SuppressLint
import android.util.Log
import java.security.SecureRandom
import java.security.cert.X509Certificate
import javax.net.ssl.HostnameVerifier
import javax.net.ssl.HttpsURLConnection
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

object SslUtils {

  @SuppressLint("CustomX509TrustManager")
  val x509TrustManager =
    object : X509TrustManager {
      @SuppressLint("TrustAllX509TrustManager")
      override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {
      }

      @SuppressLint("TrustAllX509TrustManager")
      override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
      }

      override fun getAcceptedIssuers(): Array<X509Certificate> {
        return arrayOf()
      }
    }

  val trustManager: Array<TrustManager>
    //获取TrustManager
    get() = arrayOf(
      x509TrustManager
    )

  val hostnameVerifier: HostnameVerifier = HostnameVerifier { _, _ -> true }.apply {
    HttpsURLConnection.setDefaultHostnameVerifier(this)
  }

  val sslSocketFactory: SSLSocketFactory = try {
    val sslContext = SSLContext.getInstance("SSL")
    sslContext.init(null, trustManager, SecureRandom())
    sslContext.socketFactory
  } catch (e: Exception) {
    throw RuntimeException(e)
  }.apply {
    HttpsURLConnection.setDefaultSSLSocketFactory(this)
  }

  fun trustAllCerts(){
    val sc = SSLContext.getInstance("SSL")
    sc.init(null, trustManager, null)
    HttpsURLConnection.setDefaultSSLSocketFactory(sc.socketFactory)
    val hv = HostnameVerifier { urlHostName, session ->
      Log.i("SslUtils", "Warning: URL Host: " + urlHostName + " vs. " + session.peerHost)
      true
    }
    HttpsURLConnection.setDefaultHostnameVerifier(hv)
  }
}