package cn.cleartv.terminal.widget

import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView

class FadingEdgeRecycleView(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : RecyclerView(context, attrs, defStyleAttr) {


  constructor(context: Context): this(context, null, 0)

  constructor(context: Context, attrs: AttributeSet): this(context, attrs, 0)
  override fun getTopFadingEdgeStrength(): Float {
    return super.getTopFadingEdgeStrength()
  }

  override fun getBottomFadingEdgeStrength(): Float {
    return 0f
  }

}