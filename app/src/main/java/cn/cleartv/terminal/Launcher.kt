package cn.cleartv.terminal

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.net.toUri
import cn.cleartv.terminal.log.Log
import cn.cleartv.terminal.ui.BrowserActivity
import cn.cleartv.terminal.ui.FileViewActivity
import cn.cleartv.terminal.ui.HomeActivity
import cn.cleartv.terminal.ui.SettingActivity
import cn.cleartv.terminal.utils.AppUtils
import cn.cleartv.terminal.utils.StringUtils

private fun parseParams(params: String?): Map<String, String> {
  val map = mutableMapOf<String, String>()
  params ?: return map
  if (params.isBlank()) {
    return map
  }

  params.split("&").forEach { pair ->
    val parts = pair.split("=", limit = 2)
    if (parts.size == 2 && parts[0].isNotBlank()) {
      map[parts[0]] = parts[1]
    }
  }
  return map
}

fun Context.launchApp(path: String?, params: String? = null) {
  Log.i("launchApp: $path; $params")
  val launchIntent = if (path.isNullOrBlank()) {
    showToast("启动路径未配置！")
    Intent(this, SettingActivity::class.java)
  } else {
    try {
      if (path.contains("/")) {
        // componentName
        val intent = Intent()
        val cn = AppUtils.parseComponentName(path)
        intent.component = cn
        if (!AppUtils.isAppInstalled(cn.packageName)) {
          showToast("APP未安装！")
          Intent(this, SettingActivity::class.java)
        } else if (cn.packageName == this.packageName && cn.className.contains("SplashActivity")) {
          Intent(this, HomeActivity::class.java)
        } else {
          val params = parseParams(params)
          for (i in params) {
            intent.putExtra(i.key, i.value)
          }
          intent
        }
      } else {
        // packageName
        if (!AppUtils.isAppInstalled(path)) {
          showToast("APP未安装！")
          Intent(this, SettingActivity::class.java)
        } else if (path == packageName) {
          Intent(this, HomeActivity::class.java)
        } else {
          val intent = packageManager.getLaunchIntentForPackage(path)
          if (intent == null) {
            showToast("应用无启动页面！")
            Intent(this, SettingActivity::class.java)
          } else {
            val params = parseParams(params)
            for (i in params) {
              intent.putExtra(i.key, i.value)
            }
            intent
          }
        }
      }
    } catch (e: IllegalArgumentException) {
      e.printStackTrace()
      showToast("启动路径配置错误！")
      Intent(this, SettingActivity::class.java)
    } catch (e: Exception) {
      e.printStackTrace()
      showToast("启动失败：${e.message}")
      Intent(this, SettingActivity::class.java)
    }
  }
  launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
  Log.i("launch app: $launchIntent")
  startActivity(launchIntent)
}

fun Context.launchHome() {
  Log.i("launchHome")
  Intent(this, HomeActivity::class.java).apply {
    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    startActivity(this)
  }
}

fun Context.launchFileView(uri: Uri) {
  Log.i("launchFileView: $uri")
  Intent(this, FileViewActivity::class.java).apply {
    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    putExtra("uri", uri)
    startActivity(this)
  }
}

fun Context.launchSetting() {
  Log.i("launchSetting")
  Intent(this, SettingActivity::class.java).apply {
    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    startActivity(this)
  }
}

fun Context.launchWeb(url: String) {
  Log.i("launchWeb: $url")
  Intent(this, BrowserActivity::class.java).apply {
    putExtra("url", url)
    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    startActivity(this)
  }
}

fun Context.autoLaunch() {
  Log.i("autoLaunch")
  when (LauncherConfig.type) {
    LauncherConfig.LAUNCHER_TYPE_DEFAULT -> {
      launchHome()
    }

    LauncherConfig.LAUNCHER_TYPE_WEB -> {
      val url = "${LauncherConfig.path?.trim()}${(LauncherConfig.params ?: "").trim()}"
      if (!StringUtils.matches(url, Constants.RegexString.URL)) {
        launchSetting()
      } else {
        launchWeb(url)
      }
    }

    LauncherConfig.LAUNCHER_TYPE_APP -> {
      launchApp(LauncherConfig.path, LauncherConfig.params)
    }

    LauncherConfig.LAUNCHER_TYPE_FILE -> {
      LauncherConfig.path.let {
        if (it.isNullOrBlank()) {
          launchSetting()
        } else {
          launchFileView(it.toUri())
        }
      }
    }

    else -> {
      Log.w("not support type: ${LauncherConfig.type}")
      showToast("not support type: ${LauncherConfig.type}")
      launchSetting()
    }
  }
}