package cn.cleartv.terminal

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityManager
import android.content.Context
import android.content.pm.ActivityInfo
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.hardware.display.DisplayManager
import android.media.AudioManager
import android.os.Process
import android.util.DisplayMetrics
import android.util.Log
import android.widget.Toast
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import cn.cleartv.terminal.utils.EthernetManager
import com.google.gson.Gson
import com.google.gson.GsonBuilder


val applicationContext: Context by lazy { App.instance }

val audioManager: AudioManager by lazy {
  applicationContext.getSystemService(AudioManager::class.java)
}

val packageName: String by lazy { App.instance.packageName }

val packageManager: PackageManager by lazy {
  applicationContext.packageManager
}

val packageInfo: PackageInfo by lazy {
  packageManager.getPackageInfo(packageName, 0)
}

val applicationInfo: ApplicationInfo by lazy {
  packageManager.getApplicationInfo(packageName, 0)
}

val displayManager: DisplayManager by lazy {
  applicationContext.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
}

val activityManager: ActivityManager by lazy {
  applicationContext.getSystemService("activity") as ActivityManager
}

val ethernetManager: EthernetManager by lazy {
  EthernetManager(applicationContext)
}

val isDebug: Boolean by lazy {
  (applicationInfo.flags and ApplicationInfo.FLAG_DEBUGGABLE != 0 || Log.isLoggable(
    "Clear",
    Log.DEBUG
  ))
}

val isSystemApp: Boolean by lazy {
  var result = false
  try {
    // 获取应用程序签名信息
    val signature = packageManager.getPackageInfo(
      packageName,
      PackageManager.GET_SIGNATURES
    ).signatures?.firstOrNull()

    // 获取系统签名信息
    val systemSignature = packageManager.getPackageArchiveInfo(
      "/system/framework/framework-res.apk",
      PackageManager.GET_SIGNATURES
    )?.signatures?.firstOrNull()
    result = if (signature == null || systemSignature == null) {
      false
    } else {
      signature.toByteArray().contentEquals(systemSignature.toByteArray())
    }
  } catch (e: PackageManager.NameNotFoundException) {
    e.printStackTrace()
  }
  result
}

val displayMetrics
  get() = displayManager.displays.map {
    DisplayMetrics().apply {
      it.getRealMetrics(this)
    }
  }.toList()

fun Context.isTabletMode(): Boolean {
  return resources.getBoolean(R.bool.isTablet)
}

fun Context.isPhoneMode(): Boolean {
  return !isTabletMode()
}

fun Context.hasPermission(permission: String): Boolean {
  return ContextCompat.checkSelfPermission(
    this,
    permission
  ) == PackageManager.PERMISSION_GRANTED
}

fun Context.showDebugToast(@StringRes resId: Int) {
  if (isDebug) {
    Toast.makeText(this, resId, Toast.LENGTH_SHORT).show()
  }
}

fun Context.showDebugToast(message: String) {
  if (isDebug) {
    Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
  }
}

fun Context.showToast(@StringRes resId: Int) {
  Toast.makeText(this, resId, Toast.LENGTH_SHORT).show()
}

fun Context.showToast(message: String) {
  Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
}

fun Activity.enableFullScreen() {
  val controller = WindowInsetsControllerCompat(window, window.decorView)
  controller.hide(WindowInsetsCompat.Type.navigationBars())
  controller.hide(WindowInsetsCompat.Type.statusBars())
  // Some oneplus, huawei devices rely on this line of code for full screen
  controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
}

@SuppressLint("SourceLockedOrientationActivity", "WrongConstant")
fun Activity.lockPortraitOrientation() {
  if (isPhoneMode()) {
    this.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
  } else {
    this.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT
  }
}

@SuppressLint("WrongConstant")
fun Activity.lockLandscapeOrientation() {
  this.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
}

@SuppressLint("WrongConstant")
fun Activity.unlockOrientation() {
  this.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
}

fun <T> timeIt(methodName: String, block: () -> T): T {
  val start = System.currentTimeMillis()
  val result = block()
  val end = System.currentTimeMillis()
  Log.d("TimeIt", "Method $methodName took ${end - start}ms")
  return result
}

val gson = Gson()

val formatGson = GsonBuilder().setPrettyPrinting().create()
inline fun <reified T> String.fromJson(): T {
  return gson.fromJson(this, T::class.java)
}

fun <T> String.fromJson(clazz: Class<T>): T {
  return gson.fromJson(this, clazz)
}

fun Any.toJson(format: Boolean = false): String {
  return if (format) formatGson.toJson(this) else gson.toJson(this)
}

fun Context.isProcess(processName: String): Boolean {
  var currentProcName = ""
  val manager = getSystemService("activity") as ActivityManager
  for (processInfo in manager.runningAppProcesses) {
    if (processInfo.pid == Process.myPid()) {
      currentProcName = processInfo.processName
      break
    }
  }
  return currentProcName.endsWith(processName)
}
