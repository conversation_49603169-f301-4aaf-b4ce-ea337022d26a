package cn.cleartv.terminal.localserver.api

import androidx.core.graphics.drawable.toBitmap
import cn.cleartv.terminal.Dirs
import cn.cleartv.terminal.localserver.annotation.Authority
import cn.cleartv.terminal.localserver.annotation.RequestBody
import cn.cleartv.terminal.localserver.annotation.RequestMapping
import cn.cleartv.terminal.localserver.annotation.RequestMethod
import cn.cleartv.terminal.localserver.annotation.RequestParams
import cn.cleartv.terminal.localserver.annotation.Service
import cn.cleartv.terminal.localserver.model.ApkInfo
import cn.cleartv.terminal.localserver.model.DeviceInfo
import cn.cleartv.terminal.localserver.model.ResponseData
import cn.cleartv.terminal.platform.PlatformManager
import cn.cleartv.terminal.utils.AppUtils
import cn.cleartv.terminal.utils.BitmapUtils
import cn.cleartv.terminal.utils.FileUtils
import cn.cleartv.terminal.utils.Utils
import java.io.File

/**
 *
 * 通用接口，请求路径为`/api/common`
 *
 * 支持POST和GET两种请求，每次请求只上传一个文件
 *
 * [Service] path: 请求路径
 *
 * [Authority] 需要认证
 *
 * [RequestMapping] method: 请求类型；uri: 请求路径
 *
 * [RequestBody] 标注的参数为File时，请求体为form-data，其他类型请求体为对象对应转换的Json字符串。required是否必传，默认为true
 *
 * [RequestParams] 标注的参数对应url中的参数。name为参数名；required是否必传，默认为true；defaultValue为非必传时的默认参数值
 *
 * 通常方法名为接口路径，例如[get]对应接口 /api/get
 *
 * <AUTHOR>
 */
@Service(path = "/common")
object CommonService {

  /**
   * 截屏
   * `GET /api/common/screenshot`
   *
   */
  @Authority
  @RequestMapping(RequestMethod.GET, "/screenshot")
  fun screenshot(): File {
    val tempScreenshot = File(Dirs.tempDir, "screenshot.png")
    if (System.currentTimeMillis() - FileUtils.getFileLastModified(tempScreenshot) > 3000) {
      // 3秒内不重复截屏
      PlatformManager.platform.takeScreenshot(tempScreenshot.absolutePath)
    }
    return tempScreenshot
  }


  /**
   * 安装apk
   * ` POST /api/common/installApk `
   *
   * @param file   The apk file.
   * @param params The params of installation(e.g.,<code>-r</code>, <code>-s</code>).
   */
  @Authority
  @RequestMapping(RequestMethod.POST, "/installApk")
  fun installApk(
    @RequestBody file: File,
    @RequestParams(
      name = "params",
      required = false,
      defaultValue = "-r"
    ) params: String,
  ): ResponseData<ApkInfo> {
    val appInfo = AppUtils.getApkInfo(file)
      ?: return ResponseData(code = 500, message = "APK 解析失败！")
    val apkInfo = ApkInfo(
      appInfo.packageName,
      appInfo.name,
      appInfo.versionCode,
      appInfo.versionName,
      appInfo.isSystem,
      BitmapUtils.compressBitmap2Base64(appInfo.icon.toBitmap())
    )
    val isSuccess = AppUtils.installAppSilent(file, params)
    return ResponseData(message = if (isSuccess) "安装成功" else "安装失败", data = apkInfo)
  }

  /**
   * 替换开机动画
   * ` POST /api/common/replaceBootAnimation `
   *
   * @param file 开机动画文件.
   */
  @Authority
  @RequestMapping(RequestMethod.POST, "/replaceBootAnimation")
  fun replaceBootAnimation(
    @RequestBody file: File,
  ): ResponseData<Boolean> {
    val isSuccess = PlatformManager.platform.replaceBootAnimation(file.absolutePath)
    return ResponseData(message = "替换成功，重启设备检测是否生效", data = isSuccess)
  }


  /**
   * 获取设备信息
   * ` GET /api/common/getDeviceInfo `
   *
   * @return true
   */
  @RequestMapping(RequestMethod.GET, "/getDeviceInfo")
  fun getDeviceInfo(): ResponseData<DeviceInfo> {
    return ResponseData(data = DeviceInfo())
  }


  /**
   * 重启设备
   * ` GET /api/common/reboot `
   *
   * @param delayMillis 延时毫秒值，默认3000ms
   */
  @Authority
  @RequestMapping(RequestMethod.GET, "/reboot")
  fun reboot(
    @RequestParams(
      name = "delayMillis",
      required = false,
      defaultValue = "3000"
    ) delayMillis: Long = 3000L
  ): ResponseData<Boolean> {
    Utils.runOnUiThreadDelayed({ PlatformManager.platform.reboot() }, delayMillis)
    return ResponseData(data = true)
  }

  /**
   * 关机
   * ` GET /api/common/shutdown `
   *
   * @param delayMillis 延时毫秒值，默认3000ms
   */
  @Authority
  @RequestMapping(RequestMethod.GET, "/shutdown")
  fun shutdown(
    @RequestParams(
      name = "delayMillis",
      required = false,
      defaultValue = "3000"
    ) delayMillis: Long = 3000L
  ): ResponseData<Boolean> {
    Utils.runOnUiThreadDelayed({ PlatformManager.platform.shutdown() }, delayMillis)
    return ResponseData(data = true)
  }

  /**
   * 开启网络ADB
   * ` GET /api/common/networkAdb `
   *
   * @param open 是否开启
   * @param port ADB端口
   */
  @Authority
  @RequestMapping(RequestMethod.GET, "/networkAdb")
  fun networkAdb(
    @RequestParams(name = "open") open: Boolean,
    @RequestParams(name = "port", required = false, defaultValue = "5555") port: Int = 5555
  ): ResponseData<Boolean> {
    PlatformManager.platform.openNetworkAdb(open, port)
    return ResponseData(data = true)
  }

}