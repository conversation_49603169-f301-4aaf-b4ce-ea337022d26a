package cn.cleartv.terminal.localserver.api

import cn.cleartv.terminal.localserver.annotation.RequestMethod
import cn.cleartv.terminal.localserver.annotation.RequestMapping
import cn.cleartv.terminal.AuthFailedError
import cn.cleartv.terminal.localserver.annotation.Authority
import cn.cleartv.terminal.localserver.annotation.RequestParams
import cn.cleartv.terminal.localserver.annotation.Service
import cn.cleartv.terminal.localserver.model.Session
import cn.cleartv.terminal.log.Log
import cn.cleartv.terminal.utils.DataStoreUtils
import cn.cleartv.terminal.utils.EncryptUtils
import cn.cleartv.terminal.utils.StringUtils

/**
 * 认证相关接口，请求路径为`/api/auth`
 *
 * [Service] path: 请求根路径
 *
 * [RequestMapping] method: 请求类型；uri: 请求路径
 *
 * [RequestBody] 标注的参数为File时，请求体为form-data，其他类型请求体为对象对应转换的Json字符串。required是否必传，默认为true
 *
 * [RequestParams] 标注的参数对应url中的参数。name为参数名；required是否必传，默认为true；defaultValue为非必传时的默认参数值
 *
 * <AUTHOR>
 */
@Service(path = "/auth")
object AuthService {

  private val localAuthDataStore by lazy { DataStoreUtils.getDataStore("local_auth") }

  /**
   * @suppress
   */
  val tokenHolder = ThreadLocal<String>()

  /**
   * @suppress
   */
  val sessions: MutableMap<String, Session> = mutableMapOf()

  /**
   * @suppress
   */
  private var username: String
    get() = localAuthDataStore.readStringData("auth_username") ?: "admin"
    set(value) {
      localAuthDataStore.putSyncData("auth_username", value)
    }

  /**
   * @suppress
   */
  private var password: String
    get() = localAuthDataStore.readStringData("auth_password")
      ?: "admin123"
    set(value) {
      localAuthDataStore.putSyncData("auth_password", value)
    }


  /**
   * 通过第三方token认证，可用于使用后台登录的token认证
   * ` GET /api/auth/loginByToken `
   *
   */
  @RequestMapping(RequestMethod.GET, "/loginByToken")
  fun loginByToken(
    @RequestParams(name = "realm") realm: String,
    @RequestParams(name = "token") token: String,
  ): Session {
    // todo: check token from server, 这里暂时写死
    if (realm != "clear" && token != "3572b892b1e663cc6ed51a37ae0ffc61") throw AuthFailedError("auth token failed!")
    // 一小时有效期
    val session = Session(StringUtils.getRandomStr(32), 3600)
    sessions[session.id] = session
    Log.i("login: ${session.id}")
    return session
  }

  /**
   * 登录
   * ` GET /api/auth/login `
   *
   * @param username 用户名
   * @param password 密码，需要MD5
   */
  @RequestMapping(RequestMethod.GET, "/login")
  fun login(
    @RequestParams(name = "username") username: String,
    @RequestParams(name = "password") password: String,
  ): Session {
    if (this.username == username && this.password.equals(password, true)) {
      // 一小时有效期
      val session = Session(StringUtils.getRandomStr(32),  3600)
      sessions[session.id] = session
      Log.i("login: ${session.id}")
      return session
    } else {
      throw AuthFailedError("Username or password error")
    }
  }

  /**
   * 退出登录
   * ` GET /api/auth/logout `
   *
   */
  @Authority
  @RequestMapping(RequestMethod.GET, "/logout")
  fun logout(): Boolean {
    val token = tokenHolder.get()
    Log.i("logout: $token")
    token?.let { sessions.remove(it) }
    return true
  }

  /**
   * 修改密码
   * ` GET /api/auth/changePassword `
   *
   */
  @Authority
  @RequestMapping(RequestMethod.GET, "/changePassword")
  fun changePassword(
    @RequestParams(name = "oldPassword") oldPassword: String,
    @RequestParams(name = "newPassword") newPassword: String,
  ): Boolean {
    if (oldPassword == password) {
      password = newPassword
      return true
    } else {
      throw AuthFailedError("Password not correct!")
    }
  }

  /**
   * 修改登录名
   * ` GET /api/auth/changeUsername `
   *
   */
  @Authority
  @RequestMapping(RequestMethod.GET, "/changeUsername")
  fun changeUsername(
    @RequestParams(name = "oldUsername") oldUsername: String,
    @RequestParams(name = "newUsername") newUsername: String,
  ): Boolean {
    if (oldUsername == username) {
      username = newUsername
      return true
    } else {
      throw AuthFailedError("Username not correct!")
    }
  }

  /**
   * @suppress
   */
  @Throws(AuthFailedError::class)
  fun checkAuth(token: String) {
    Log.i("checkAuth: $token")
    sessions[token]?.let {
      tokenHolder.set(it.id)
      if (System.currentTimeMillis() - it.createTime > it.expiresIn * 1000) {
        throw AuthFailedError("Token expired")
      }
    } ?: throw AuthFailedError("Auth failed")
  }


  /**
   * 清理过期的session
   * ` GET /api/auth/clearExpiredSession `
   *
   */
  @RequestMapping(RequestMethod.GET, "/clearExpiredSession")
  fun clearExpiredSession() {
    val before = sessions.size
    sessions.entries.removeAll {
      System.currentTimeMillis() - it.value.createTime > it.value.expiresIn * 1000
    }
    Log.i("clear expired session: ${before - sessions.size}")
  }

}