package cn.cleartv.terminal.localserver

import cn.cleartv.terminal.AuthFailedError
import cn.cleartv.terminal.BadRequestException
import cn.cleartv.terminal.Dirs
import cn.cleartv.terminal.MethodNotAllowedException
import cn.cleartv.terminal.ServiceMethodError
import cn.cleartv.terminal.applicationInfo
import cn.cleartv.terminal.fromJson
import cn.cleartv.terminal.localserver.annotation.Authority
import cn.cleartv.terminal.localserver.annotation.RequestBody
import cn.cleartv.terminal.localserver.annotation.RequestMapping
import cn.cleartv.terminal.localserver.annotation.RequestParams
import cn.cleartv.terminal.localserver.annotation.Service
import cn.cleartv.terminal.localserver.api.AuthService
import cn.cleartv.terminal.localserver.model.ResponseData
import cn.cleartv.terminal.localserver.model.Session
import cn.cleartv.terminal.localserver.nanohttpd.NanoHTTPD
import cn.cleartv.terminal.localserver.nanohttpd.NanoHTTPD.getMimeTypeForFile
import cn.cleartv.terminal.localserver.nanohttpd.NanoHTTPD.newFixedLengthResponse
import cn.cleartv.terminal.log.Log
import cn.cleartv.terminal.toJson
import dalvik.system.DexFile
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.lang.reflect.InvocationTargetException
import java.lang.reflect.Method
import java.lang.reflect.ParameterizedType
import java.util.Locale
import java.util.UUID

/**
 *
 * 服务管理
 *
 * 所有Http的服务需要在这里注册才能使用
 *
 */
object ApiParser {

  const val ROOT_PATH = "/api"
  const val VERSION = "1.0"
  const val TOKEN_KEY = "Authorization"

  private val pathServicePath: HashMap<String, Any> = hashMapOf()
  private val pathGetMethod: HashMap<String, Method> = hashMapOf()
  private val pathPostMethod: HashMap<String, Method> = hashMapOf()

  fun normalizeUri(value: String?): String {
    if (value == null) {
      return ""
    }
    return value.trim().replace("/{2,}".toRegex(), "/")
  }

  fun registerServiceFromPackage(packageNames: Array<String>) {
    getObjectsInPackage(packageNames).forEach {
      registerService(it)
    }
  }

  fun registerService(service: Any) {
    Log.i("registerService: $service")
    service::class.java.getAnnotation(Service::class.java)?.let {
      val rootPath = normalizeUri(ROOT_PATH + "/" + it.path).removeSuffix("/")
      pathServicePath[rootPath] = service
      for (m in service::class.java.methods) {
        val requestMapping = m.getAnnotation(RequestMapping::class.java) ?: continue
        m.isAccessible = true
        var path = requestMapping.uri
        if (path.isNotBlank() && !path.startsWith("/")) {
          path = "/$path"
        }
        val methodUrl = "$rootPath$path"
        when (requestMapping.requestMethod) {
          "GET" -> {
            pathGetMethod[methodUrl] = m
          }

          "POST" -> {
            pathPostMethod[methodUrl] = m
          }
        }
      }
    }
  }

  @Throws(AuthFailedError::class)
  fun checkAuth(session: NanoHTTPD.IHTTPSession) {
    // 认证
    session.headers[TOKEN_KEY.lowercase(Locale.ROOT)]?.let {
      AuthService.checkAuth(it)
    } ?: throw AuthFailedError("Missing token")
  }

  fun invokeRequest(session: NanoHTTPD.IHTTPSession): NanoHTTPD.Response {
    val uri = session.uri
    val realUri = normalizeUri(uri)
    val method = session.method
    val parameters = session.parameters
    val remoteHostName = session.remoteHostName
    val remoteIpAddress = session.remoteIpAddress
    val headers = session.headers
    val bodyMap = hashMapOf<String, String>()
    session.parseBody(bodyMap)
    Log.d(
      "uri: $uri, \n" +
          "realUri: $realUri, \n" +
          "method: $method, \n" +
          "headers: $headers, \n" +
          "parameters: $parameters, \n" +
          "bodyMap: $bodyMap, \n" +
          "remoteHostName: $remoteHostName, \n" +
          "remoteIpAddress: $remoteIpAddress"
    )
    val m = when (method) {
      NanoHTTPD.Method.GET -> {
        pathGetMethod[realUri]
      }

      NanoHTTPD.Method.POST -> {
        pathPostMethod[realUri]
      }

      else -> {
        throw MethodNotAllowedException("Only support GET and POST")
      }
    }
    m ?: throw BadRequestException("Unknown path: $realUri")

    m.getAnnotation(Authority::class.java)?.let {
      checkAuth(session)
    }

    // 保存method所需要的参数
    val paramsArray: Array<Any?> = Array(m.parameterTypes.size) { null }
    val service = pathServicePath.keys.find { realUri.startsWith(it) }?.let { pathServicePath[it] }
      ?: throw BadRequestException("Unknown path: $realUri")
    for (i in 0 until m.parameterTypes.size) {
      // 获取参数上的所有注解，并找到RequestParams、RequestBody注解
      val requestAnnotation = m.parameterAnnotations[i].firstOrNull { item ->
        item is RequestParams || item is RequestBody
      } ?: throw ServiceMethodError("Method params not set annotation")
      when (requestAnnotation) {
        is RequestParams -> {
          val parameterType = m.parameterTypes[i]
          paramsArray[i] = parseRequestParam(
            requestAnnotation,
            parameterType,
            m.genericParameterTypes[i] as? ParameterizedType,
            parameters
          )
        }

        is RequestBody -> {
          val parameterType = m.parameterTypes[i]
          paramsArray[i] = parseRequestBody(
            requestAnnotation,
            parameterType,
            parameters, bodyMap
          )
        }
      }
    }
    Log.d("invoke $service: ${m.name}(${paramsArray.contentToString()})")
    val response = try {
      m.invoke(service, *paramsArray)
    } catch (e: InvocationTargetException) {
      throw e.cause ?: e
    }
    Log.d("return: ${response})")
    when (response) {
      is NanoHTTPD.Response -> return response
      is File -> return newFixedLengthResponse(
        NanoHTTPD.Response.Status.OK,
        getMimeTypeForFile(response.name),
        FileInputStream(response),
        response.length().toInt().toLong()
      ).apply {
        addHeader("Accept-Ranges", "bytes")
        // 设置在浏览器中展示，而不是下载
        addHeader("Content-Disposition", "inline")
      }

      is ResponseData<*> -> return newFixedLengthResponse(
        NanoHTTPD.Response.Status.OK,
        "application/json",
        response.toJson()
      )

      else -> return newFixedLengthResponse(
        NanoHTTPD.Response.Status.OK,
        "application/json",
        ResponseData(data = response).toJson()
      ).apply {
        if (response is Session) {
          addHeader(TOKEN_KEY, response.id)
        }
      }
    }
  }

  private fun parseRequestParam(
    requestParams: RequestParams,
    parameterType: Class<*>,
    genericParameterType: ParameterizedType?,
    parameters: Map<String, List<String>>,
  ): Any? {
    if (requestParams.required && !parameters.containsKey(requestParams.name)) throw BadRequestException(
      "Missing request params"
    )
    when (parameterType) {
      String::class.java -> {
        return parameters[requestParams.name]?.firstOrNull() ?: requestParams.defaultValue
      }

      Boolean::class.java -> {
        return parameters[requestParams.name]?.firstOrNull()?.lowercase(Locale.getDefault())
          ?.toBooleanStrictOrNull() ?: requestParams.defaultValue.toBoolean()
      }

      Int::class.java -> {
        return parameters[requestParams.name]?.firstOrNull()?.toIntOrNull()
          ?: requestParams.defaultValue.toIntOrNull()
      }

      Long::class.java -> {
        return parameters[requestParams.name]?.firstOrNull()?.toLongOrNull()
          ?: requestParams.defaultValue.toLongOrNull()
      }

      Double::class.java -> {
        return parameters[requestParams.name]?.firstOrNull()?.toDoubleOrNull()
          ?: requestParams.defaultValue.toDoubleOrNull()
      }

      Float::class.java -> {
        return parameters[requestParams.name]?.firstOrNull()?.toFloatOrNull()
          ?: requestParams.defaultValue.toFloatOrNull()
      }

      List::class.java -> {
        when (genericParameterType!!.actualTypeArguments[0]) {
          String::class.java -> {
            return parameters[requestParams.name] ?: requestParams.defaultValue.split(",")
          }

          Int::class.java -> {
            return parameters[requestParams.name]?.map { it.toIntOrNull() }
              ?: requestParams.defaultValue.split(",").map { it.toIntOrNull() }
          }

          Long::class.java -> {
            return parameters[requestParams.name]?.map { it.toLongOrNull() }
              ?: requestParams.defaultValue.split(",").map { it.toLongOrNull() }
          }

          Double::class.java -> {
            return parameters[requestParams.name]?.map { it.toDoubleOrNull() }
              ?: requestParams.defaultValue.split(",").map { it.toDoubleOrNull() }
          }

          Float::class.java -> {
            return parameters[requestParams.name]?.map { it.toFloatOrNull() }
              ?: requestParams.defaultValue.split(",").map { it.toFloatOrNull() }
          }

          else -> throw ServiceMethodError("Method only support type: String, Int, Long, Float, Double or their List")
        }
      }

      else -> throw ServiceMethodError("Method only support type: String, Int, Long, Float, Double or their List")
    }
  }


  private fun parseRequestBody(
    requestBody: RequestBody,
    parameterType: Class<*>,
    parameters: Map<String, List<String>>,
    bodyMap: Map<String, String>
  ): Any? {
    when (parameterType) {
      File::class.java -> {
        // 每次只能上传一个文件
        val iterator = bodyMap.iterator()
        if (!iterator.hasNext()) {
          if (requestBody.required) {
            throw BadRequestException("No selected file")
          } else {
            return null
          }
        }
        val tempFileInfo = iterator.next()
        val tempFile = File(tempFileInfo.value)
        val fileName =
          parameters[tempFileInfo.key]?.firstOrNull() ?: extractFileName(tempFile)
          ?: UUID.randomUUID()
            .toString()
        val targetFile = File(Dirs.tempDir, fileName)
        Log.i("Save file: $tempFile to $targetFile")
        if (targetFile.exists()) {
          // 覆盖旧文件
          targetFile.delete()
        }
        tempFile.copyTo(targetFile)
        return targetFile

      }

      String::class.java -> {
        val postData = bodyMap["postData"]
        if (requestBody.required && postData == null) throw BadRequestException("Missing request body")
        return postData
      }

      else -> {
        val postData = bodyMap["postData"]
        if (requestBody.required && postData == null) throw BadRequestException("Missing request body")
        return postData?.fromJson(parameterType)
      }
    }
  }

  private fun extractFileName(tempFile: File): String? {
    try {
      val fileContent = tempFile.readText()
      val fileNameLine = fileContent.lines().firstOrNull { it.contains("filename=") }
      if (fileNameLine != null) {
        val fileName = fileNameLine.substringAfter("filename=\"").substringBefore("\"")
        return fileName
      }
    } catch (e: IOException) {
      e.printStackTrace()
    }
    return null
  }

  @Suppress("DEPRECATION")
  private fun scanDexFile(
    dexPath: String,
    packageNames: Array<String>,
    classNames: MutableList<String>
  ) {
    val dexFile = DexFile(dexPath)
    try {
      val entries = dexFile.entries()
      while (entries.hasMoreElements()) {
        val className = entries.nextElement()
        for (packageName in packageNames) {
          if (className.startsWith(packageName)) {
            classNames.add(className)
            break // 一旦匹配到一个包名就跳出循环
          }
        }
      }
    } finally {
      try {
        dexFile.close()
      } catch (e: Exception) {
        // 忽略关闭异常
      }
    }
  }

  private fun getAllClasses(packageNames: Array<String>): List<String> {
    val classNames = mutableListOf<String>()
    try {
      // 这里可能需要使用反射来获取DexFile中的类列表
      scanDexFile(applicationInfo.sourceDir, packageNames, classNames)
      applicationInfo.splitSourceDirs?.forEach { splitSourceDir ->
        scanDexFile(splitSourceDir, packageNames, classNames)
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
    return classNames
  }

  fun getObjectsInPackage(packageNames: Array<String>): List<Any> {
    val objects = mutableListOf<Any>()
    val allClasses = getAllClasses(packageNames)
    allClasses.forEach { className ->
      try {
        addObject(className, objects)
      } catch (_: Exception) {
      }
    }
    return objects
  }

  private fun addObject(className: String, objects: MutableList<Any>) {
    try {
      val clazz = Class.forName(className)
      // 检查是否有 @Service 注解
      val serviceAnnotation = clazz.getAnnotation(Service::class.java)

      if (serviceAnnotation != null) {
        // 如果有 @Service 注解，检查是否为 Kotlin object（通过 INSTANCE 字段判断）
        try {
          val instanceField = clazz.getDeclaredField("INSTANCE")
          val instance = instanceField.get(null)
          objects.add(instance!!)
        } catch (_: NoSuchFieldException) {
          // 如果没有 INSTANCE 字段，说明是普通的类，创建新实例
          val instance = clazz.getDeclaredConstructor().newInstance()
          objects.add(instance)
        }
      }
    } catch (e: Exception) {
      // 忽略不是 object 的类
    }
  }

}