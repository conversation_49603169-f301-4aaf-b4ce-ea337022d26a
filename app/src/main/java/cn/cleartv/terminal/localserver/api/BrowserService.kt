package cn.cleartv.terminal.localserver.api

import cn.cleartv.terminal.App
import cn.cleartv.terminal.Dirs
import cn.cleartv.terminal.localserver.annotation.Authority
import cn.cleartv.terminal.localserver.annotation.RequestBody
import cn.cleartv.terminal.localserver.annotation.RequestMapping
import cn.cleartv.terminal.localserver.annotation.RequestMethod.Companion.POST
import cn.cleartv.terminal.localserver.annotation.RequestParams
import cn.cleartv.terminal.localserver.annotation.Service
import cn.cleartv.terminal.localserver.model.BrowserMessage
import cn.cleartv.terminal.localserver.model.ResponseData
import cn.cleartv.terminal.toJson
import cn.cleartv.terminal.ui.BrowserActivity
import cn.cleartv.terminal.utils.ActivityUtils
import cn.cleartv.terminal.utils.Utils
import cn.cleartv.terminal.utils.ZipUtils
import com.tencent.smtt.sdk.QbSdk
import java.io.File
import kotlin.collections.filterIsInstance
import kotlin.collections.forEach
import kotlin.text.endsWith

/**
 *
 * 浏览器相关接口，请求路径为`/api/browser`
 *
 * 支持POST和GET两种请求
 *
 * [Service] path: 请求路径
 *
 * [Authority] 需要认证
 *
 * [RequestMapping] method: 请求类型；uri: 请求路径
 *
 * [RequestBody] 标注的参数为File时，请求体为form-data，其他类型请求体为对象对应转换的Json字符串。required是否必传，默认为true
 *
 * [RequestParams] 标注的参数对应url中的参数。name为参数名；required是否必传，默认为true；defaultValue为非必传时的默认参数值
 *
 * <AUTHOR>
 */
@Service(path = "/browser")
object BrowserService {

  /**
   * 发送消息给内部浏览器
   * ` POST /api/browser/sendMessage `
   *
   * 前端可通过定义onBrowserMessage(type, content)来监听
   *
   * 相当于执行 javascript:onBrowserMessage(type, content)
   *
   * body: [BrowserMessage]
   *
   * @param browserMessage 消息内容
   * @return true
   */
  @Authority
  @RequestMapping(POST, "/sendMessage")
  fun sendBrowserMessage(
    @RequestBody browserMessage: BrowserMessage
  ): ResponseData<Boolean> {
    // 网页消息，通知网页
    Utils.runOnUiThread {
      ActivityUtils.getActivityList().filterIsInstance<BrowserActivity>().forEach {
        it.webView.evaluateJavascript(
          "javascript:onBrowserMessage('${browserMessage.toJson()}')"
        ) { }
      }
    }
    return ResponseData(data = true)
  }


  /**
   * 安装TBS内核
   * ` POST /api/browser/installTbsCore `
   *
   * @param file 内核文件.
   */
  @Authority
  @Suppress("DEPRECATION")
  @RequestMapping(POST, "/installTbsCore")
  fun installTbsCore(
    @RequestBody file: File,
  ): ResponseData<Boolean> {
    // 安装TBS内核
    QbSdk.reset(App.instance)
    QbSdk.installLocalTbsCore(App.instance, 0, file.absolutePath)
    return ResponseData(message = "TBS内核加载成功，重启应用检测是否生效", data = true)
  }


  /**
   * 更新frontend资源
   * ` POST /api/browser/updateFrontend `
   *
   * @param file 前端ZIP包.
   * @param path 前端路径
   */
  @Authority
  @RequestMapping(POST, "/updateFrontend")
  fun updateFrontend(
    @RequestBody file: File,
    @RequestParams(
      name = "path",
      required = false,
      defaultValue = ""
    ) path: String,
  ): ResponseData<Boolean> {
    if (file.name.endsWith(".zip", true)) {
      // 前端更新包
      ZipUtils.unzipFile(file, File(Dirs.frontendDir, path))
      return ResponseData(data = true)
    } else {
      return ResponseData(
        code = 500,
        message = "仅支持ZIP文件",
        data = false
      )
    }
  }
}