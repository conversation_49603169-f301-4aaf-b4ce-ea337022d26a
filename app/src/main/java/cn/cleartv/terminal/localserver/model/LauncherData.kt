package cn.cleartv.terminal.localserver.model

import cn.cleartv.terminal.LauncherConfig

data class LauncherData(
  @LauncherConfig.LauncherType
  val type: String = LauncherConfig.type,
  val path: String? = LauncherConfig.path,
  val params: String? = LauncherConfig.params
) {
  fun saveToLocal() {
    LauncherConfig.type = type
    LauncherConfig.path = path
    LauncherConfig.params = params
  }
}