package cn.cleartv.terminal.localserver.api

import cn.cleartv.terminal.Configs
import cn.cleartv.terminal.LauncherConfig
import cn.cleartv.terminal.localserver.annotation.Authority
import cn.cleartv.terminal.localserver.annotation.RequestBody
import cn.cleartv.terminal.localserver.annotation.RequestMapping
import cn.cleartv.terminal.localserver.annotation.RequestMethod.Companion.GET
import cn.cleartv.terminal.localserver.annotation.RequestMethod.Companion.POST
import cn.cleartv.terminal.localserver.annotation.RequestParams
import cn.cleartv.terminal.localserver.annotation.Service
import cn.cleartv.terminal.localserver.model.LauncherData
import cn.cleartv.terminal.localserver.model.ResponseData
import cn.cleartv.terminal.utils.AppUtils
import cn.cleartv.terminal.utils.Utils


/**
 *
 * 相关设置，请求路径为`/api/config`
 *
 * 支持POST和GET两种请求
 *
 * [Service] path: 请求路径
 *
 * [Authority] 需要认证
 *
 * [RequestMapping] method: 请求类型；uri: 请求路径
 *
 * [RequestBody] 标注的参数为File时，请求体为form-data，其他类型请求体为对象对应转换的Json字符串。required是否必传，默认为true
 *
 * [RequestParams] 标注的参数对应url中的参数。name为参数名；required是否必传，默认为true；defaultValue为非必传时的默认参数值
 *
 * <AUTHOR>
 */
@Service(path = "/config")
object ConfigService {

  /**
   * 获取配置信息
   * ` GET /api/config `
   *
   * @return 配置信息
   */
  @Authority
  @RequestMapping(GET, "")
  fun getConfig(): ResponseData<Configs> {
    return ResponseData(
      data = Configs()
    )
  }


  /**
   * 设置配置信息
   * ` POST /api/config `
   *
   * body: [Configs]
   *
   * @param autoRoot 自动重启
   * @param configs 配置信息，只需传递要修改的配置项
   * @return "设置成功，3s后重启"
   */
  @Authority
  @RequestMapping(POST, "")
  fun setLauncher(
    @RequestParams(name = "autoRoot") autoRoot: Boolean = false,
    @RequestBody configs: Configs
  ): ResponseData<String> {
    configs.saveToLocal()
    if(autoRoot) {
      Utils.runOnUiThreadDelayed({ AppUtils.relaunchApp() }, 3000)
    }
    return ResponseData(data = "设置成功${if(autoRoot) "，3s后重启" else ""}!")
  }

}