package cn.cleartv.terminal.localserver.api

import cn.cleartv.terminal.LauncherConfig
import cn.cleartv.terminal.localserver.annotation.Authority
import cn.cleartv.terminal.localserver.annotation.RequestBody
import cn.cleartv.terminal.localserver.annotation.RequestMapping
import cn.cleartv.terminal.localserver.annotation.RequestMethod.Companion.GET
import cn.cleartv.terminal.localserver.annotation.RequestMethod.Companion.POST
import cn.cleartv.terminal.localserver.annotation.Service
import cn.cleartv.terminal.localserver.model.LauncherData
import cn.cleartv.terminal.localserver.model.ResponseData
import cn.cleartv.terminal.utils.AppUtils
import cn.cleartv.terminal.utils.Utils


/**
 *
 * 启动相关设置，请求路径为`/api/launcher`
 *
 * 支持POST和GET两种请求
 *
 * [Service] path: 请求路径
 *
 * [Authority] 需要认证
 *
 * [RequestMapping] method: 请求类型；uri: 请求路径
 *
 * [RequestBody] 标注的参数为File时，请求体为form-data，其他类型请求体为对象对应转换的Json字符串。required是否必传，默认为true
 *
 * [RequestParams] 标注的参数对应url中的参数。name为参数名；required是否必传，默认为true；defaultValue为非必传时的默认参数值
 *
 * <AUTHOR>
 */
@Service(path = "/launcher")
object LauncherService {

  /**
   * 获取启动信息
   * ` GET /api/launcher `
   *
   * @return 启动配置
   */
  @RequestMapping(GET, "")
  fun getLauncher(): ResponseData<LauncherData> {
    return ResponseData(
      data = LauncherData()
    )
  }


  /**
   * 设置启动信息
   * ` POST /api/launcher `
   *
   * body: [LauncherData]
   *
   * @param launcherData 启动配置
   * @return "设置成功，3s后重启"
   */
  @Authority
  @RequestMapping(POST, "")
  fun setLauncher(
    @RequestBody launcherData: LauncherData
  ): ResponseData<String> {
    when (launcherData.type) {
      LauncherConfig.LAUNCHER_TYPE_APP,
      LauncherConfig.LAUNCHER_TYPE_WEB,
      LauncherConfig.LAUNCHER_TYPE_FILE,
        -> {
        if (launcherData.path.isNullOrBlank()) {
          throw kotlin.Exception("path参数错误")
        }
      }

      LauncherConfig.LAUNCHER_TYPE_DEFAULT -> {
      }

      else -> {
        throw kotlin.Exception("type参数错误")
      }

    }
    launcherData.saveToLocal()
    Utils.runOnUiThreadDelayed({ AppUtils.relaunchApp() }, 3000)
    return ResponseData(data = "设置成功，3s后重启")
  }

}