package cn.cleartv.terminal.localserver.annotation

import androidx.annotation.StringDef

@Retention(AnnotationRetention.RUNTIME)
@StringDef(
  MessageSourceType.HTTP,
  MessageSourceType.JS,
  MessageSourceType.BROADCAST,
  MessageSourceType.SOCKET,
  MessageSourceType.MULTICAST,
  MessageSourceType.UNKNOWN,
)
@MustBeDocumented
annotation class MessageSourceType {
  companion object {
    const val HTTP = "HTTP"
    const val JS = "JS"
    const val BROADCAST = "BROADCAST"
    const val SOCKET = "SOCKET"
    const val MULTICAST = "MULTICAST"
    const val UNKNOWN = "UNKNOWN"
  }
}