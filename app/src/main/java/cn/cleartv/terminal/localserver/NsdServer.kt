package cn.cleartv.terminal.localserver

import android.content.Context
import android.net.nsd.NsdManager
import android.net.nsd.NsdServiceInfo
import android.os.Build
import cn.cleartv.terminal.App
import cn.cleartv.terminal.DeviceInfo
import cn.cleartv.terminal.log.Log
import cn.cleartv.terminal.packageInfo

object NsdServer : NsdManager.RegistrationListener {

  const val SERVICE_NAME = "Clear_Terminal"
  const val SERVICE_TYPE = "_clear_terminal._udp"

  // jmDNS 同样效果，支持更低版本的Android
  val nsdManager: NsdManager by lazy {
    App.instance.getSystemService(Context.NSD_SERVICE) as NsdManager
  }

  @Suppress("DEPRECATION")
  private val deviceInfo get() = mapOf(
    "id" to DeviceInfo.id,
    "mac" to DeviceInfo.mac,
    "os" to "Android ${Build.VERSION.RELEASE}",
    "manufacturer" to "${Build.MANUFACTURER}(${Build.DISPLAY})",
    "versionName" to packageInfo.versionName,
    "versionCode" to packageInfo.versionCode.toString()
  )

  // dns-sd -B _clear_terminal._udp
  // dns-sd -L "Clear_Terminal" _clear_terminal._udp local.
  fun registerService(
    port: Int = 3000,
    attributes: Map<String, String?> = emptyMap()
  ) {
    nsdManager.registerService(NsdServiceInfo().apply {
      this.serviceName = SERVICE_NAME
      this.serviceType = SERVICE_TYPE
      this.port = port
      deviceInfo.forEach { (k, v) ->
        setAttribute(k, v)
      }
      attributes.forEach { (k, v) ->
        setAttribute(k, v)
      }
    }, NsdManager.PROTOCOL_DNS_SD, this)
  }

  fun unregisterService() {
    nsdManager.unregisterService(this)
  }

  override fun onRegistrationFailed(serviceInfo: NsdServiceInfo?, errorCode: Int) {
    Log.w("onRegistrationFailed: $serviceInfo, ${errorCode.getErrorInfo()}")
  }

  override fun onUnregistrationFailed(serviceInfo: NsdServiceInfo?, errorCode: Int) {
    Log.w("onUnregistrationFailed: $serviceInfo, ${errorCode.getErrorInfo()}")
  }

  override fun onServiceRegistered(serviceInfo: NsdServiceInfo?) {
    Log.i("onServiceRegistered: $serviceInfo")
  }

  override fun onServiceUnregistered(serviceInfo: NsdServiceInfo?) {
    Log.i("onServiceUnregistered: $serviceInfo")
  }

  private fun Int.getErrorInfo(): String {
    return when (this) {
      NsdManager.FAILURE_MAX_LIMIT -> {
        "FAILURE_MAX_LIMIT"
      }

      NsdManager.FAILURE_ALREADY_ACTIVE -> {
        "FAILURE_ALREADY_ACTIVE"
      }

      NsdManager.FAILURE_BAD_PARAMETERS -> {
        "FAILURE_BAD_PARAMETERS"
      }

      NsdManager.FAILURE_INTERNAL_ERROR -> {
        "FAILURE_INTERNAL_ERROR"
      }

      NsdManager.FAILURE_OPERATION_NOT_RUNNING -> {
        "FAILURE_OPERATION_NOT_RUNNING"
      }

      else -> {
        "ERROR CODE: $this"
      }
    }
  }
}
