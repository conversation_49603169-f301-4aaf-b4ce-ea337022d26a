package cn.cleartv.terminal.localserver.model

import android.os.Build
import android.util.DisplayMetrics
import cn.cleartv.terminal.DeviceInfo
import cn.cleartv.terminal.TerminalConfig
import cn.cleartv.terminal.packageInfo
import com.google.gson.annotations.SerializedName

data class DeviceInfo(
  val id: String = DeviceInfo.id,
  val mac: String = DeviceInfo.mac,
  val ip: String = DeviceInfo.ip,
  val displayMetrics: List<DisplayMetrics> = DeviceInfo.displayMetrics,
  val cpuUsed: Float = DeviceInfo.cpuUsed,
  val usedMemory: Long = DeviceInfo.usedMemory,
  val totalMemory: Long = DeviceInfo.totalMemory,
  val availableMemory: Long = DeviceInfo.availableMemory,
  val totalInternalDiskSize: Long = DeviceInfo.totalInternalDiskSize,
  val availableInternalDiskSize: Long = DeviceInfo.availableInternalDiskSize,
  val totalExternalDiskSize: Long = DeviceInfo.totalExternalDiskSize,
  val availableExternalDiskSize: Long = DeviceInfo.availableExternalDiskSize,
  val name: String = TerminalConfig.name,
  val type: String? = TerminalConfig.type,
  val remark: String? = TerminalConfig.remark,
  val description: String? = TerminalConfig.description,
  val location: String? = TerminalConfig.location,
  val extraInfo: String? = TerminalConfig.extraInfo,
  val osType: String = "Android",
  val osVersion: String = "Android ${Build.VERSION.RELEASE}",
  val systemArchitecture: String = Build.SUPPORTED_ABIS.joinToString(","),
  val version: String = "${packageInfo.versionName}(${packageInfo.versionCode})",
)
