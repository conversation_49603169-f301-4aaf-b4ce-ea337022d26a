package cn.cleartv.terminal.assistant

import android.Manifest
import android.animation.ValueAnimator
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.pm.PackageManager
import android.graphics.PixelFormat
import android.graphics.Point
import android.os.Build
import android.view.Gravity
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import androidx.annotation.RequiresPermission
import androidx.appcompat.view.ContextThemeWrapper
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.cleartv.terminal.DeviceInfo
import cn.cleartv.terminal.R
import cn.cleartv.terminal.assistant.adapter.ChatAdapter
import cn.cleartv.terminal.assistant.model.ChatMessage
import cn.cleartv.terminal.log.Log
import cn.cleartv.terminal.ui.PermissionRequestActivity.Companion.requestPermissions
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach


class AssistantService : LifecycleService() {

  private lateinit var floatingView: View
  private lateinit var windowManager: WindowManager
  private lateinit var screenSize: Point
  private lateinit var floatParams: WindowManager.LayoutParams
  private lateinit var contentView: View
  private lateinit var contentParams: WindowManager.LayoutParams
  private lateinit var chatRecyclerView: RecyclerView
  private lateinit var chatAdapter: ChatAdapter
  private val chatMessages = mutableListOf<ChatMessage>()

  private lateinit var aiServer: AiServer

  override fun onCreate() {
    super.onCreate()
    startForeground()
    windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
    // 获取 Display 对象
    val display = windowManager.defaultDisplay
    // 创建 Point 对象用于存储屏幕尺寸
    val size = Point()
    // 获取屏幕尺寸
    display.getSize(size)
    screenSize = size

    initFloatingView()
    initContentView()
    initAiServer()
  }

  fun initAiServer() {
    aiServer = AiServer(
      "wss://192.168.3.6:1443/xiaohe-websocket/",
      DeviceInfo.id
    )

    aiServer.ttsStatusFlow.onEach {
      Log.i("TTS Status: $it")

    }.launchIn(lifecycleScope)

    aiServer.ttsSharedFlow.onEach {
      Log.i("TTS Text: $it")
      addMessage(
        ChatMessage(
          text = it,
          isUser = false,
          timestamp = System.currentTimeMillis(),
          isTyping = false
        )
      )
    }.launchIn(lifecycleScope)

    aiServer.sttSharedFlow.onEach {
      Log.i("STT Text: $it")
      addMessage(
        ChatMessage(
          text = it,
          isUser = true,
          timestamp = System.currentTimeMillis(),
          isTyping = false
        )
      )
    }.launchIn(lifecycleScope)

    aiServer.init()
//    aiServer.enablePlay(true)
  }

  fun initFloatingView() {
    floatingView = LayoutInflater.from(this).inflate(R.layout.floating_assistant_layout, null)
    floatParams = WindowManager.LayoutParams(
      WindowManager.LayoutParams.WRAP_CONTENT,
      WindowManager.LayoutParams.WRAP_CONTENT,
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY else WindowManager.LayoutParams.TYPE_PHONE,
      WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
      PixelFormat.TRANSLUCENT
    )

    floatParams.gravity = Gravity.TOP or Gravity.END
    floatParams.x = 0
    floatParams.y = screenSize.y / 10

    windowManager.addView(floatingView, floatParams)
    floatingView.setOnTouchListener(object : View.OnTouchListener {
      private var initialX = 0
      private var initialY = 0
      private var initialTouchX = 0f
      private var initialTouchY = 0f
      private var downTime: Long = 0

      override fun onTouch(view: View?, event: MotionEvent): Boolean {
        Log.i("onTouch: $event")
        when (event.action) {
          MotionEvent.ACTION_DOWN -> {
            initialX = floatParams.x
            initialY = floatParams.y
            initialTouchX = event.rawX
            initialTouchY = event.rawY
            downTime = System.currentTimeMillis()
            return true
          }

          MotionEvent.ACTION_UP -> {
            if (System.currentTimeMillis() - downTime < 300) {
              val nowX = event.rawX.toInt()
              val nowY = event.rawY.toInt()
              val movedX = nowX - initialTouchX
              val movedY = nowY - initialTouchY
              if (movedX + movedY < 100) {
                floatParams.x = initialX
                floatParams.y = initialY
                windowManager.updateViewLayout(floatingView, floatParams)
                onFloatingViewClick(view)
                return true
              }
            }
            snapToEdge()
            return true
          }

          MotionEvent.ACTION_MOVE -> {
            if (floatParams.gravity and Gravity.END == Gravity.END) {
              floatParams.x = initialX - (event.rawX - initialTouchX).toInt()
            } else {
              floatParams.x = initialX + (event.rawX - initialTouchX).toInt()
            }
            if (floatParams.gravity and Gravity.BOTTOM == Gravity.BOTTOM) {
              floatParams.y = initialY - (event.rawY - initialTouchY).toInt()
            } else {
              floatParams.y = initialY + (event.rawY - initialTouchY).toInt()
            }
            windowManager.updateViewLayout(floatingView, floatParams)
            return true
          }
        }
        return true
      }
    })
  }

  private fun snapToEdge() {
    val targetX: Int
    val currentX = floatParams.x
    val screenCenterX = screenSize.x / 2

    if (currentX + floatingView.width / 2 < screenCenterX) {
      targetX = 0
    } else {
      targetX = screenSize.x - floatingView.width
    }
    if (currentX == targetX) return
    val animator = ValueAnimator.ofInt(currentX, targetX)
    animator.duration = 300 // Animation duration in milliseconds
    animator.addUpdateListener { animation ->
      floatParams.x = animation.animatedValue as Int
      windowManager.updateViewLayout(floatingView, floatParams)
    }
    animator.start()
  }


  private fun onFloatingViewClick(view: View?) {

    switchFloatingView()
//    startActivity(Intent(this, AiAssistantActivity::class.java).apply {
//      addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//    })
  }

  private fun switchFloatingView() {
    if (floatingView.isVisible) {
      if (ContextCompat.checkSelfPermission(
          this,
          Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
      ) {
        startChat()
        floatingView.visibility = View.GONE
        windowManager.removeView(floatingView)
        windowManager.addView(contentView, contentParams)
        contentView.visibility = View.VISIBLE
      } else {
        requestPermissions(arrayOf(Manifest.permission.RECORD_AUDIO))
      }
    } else {
      stopChat()
      windowManager.addView(floatingView, floatParams)
      floatingView.visibility = View.VISIBLE
      contentView.visibility = View.GONE
      windowManager.removeView(contentView)
    }
  }

  @RequiresPermission(Manifest.permission.RECORD_AUDIO)
  private fun startChat() {
    aiServer.connect()
    aiServer.startRecording()
    aiServer.enablePlay(true)
  }

  private fun stopChat() {
    aiServer.stopRecording()
    aiServer.enablePlay(false)
    aiServer.disconnect()
    chatMessages.clear()
    chatAdapter.notifyDataSetChanged()
  }

  fun initContentView() {
    contentView = LayoutInflater.from(ContextThemeWrapper(this, R.style.TransparentStyle))
      .inflate(R.layout.assistant_content_layout, null)
    chatRecyclerView = contentView.findViewById(R.id.chatRecyclerView)

    contentParams = WindowManager.LayoutParams(
      WindowManager.LayoutParams.MATCH_PARENT,
      WindowManager.LayoutParams.MATCH_PARENT,
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY else WindowManager.LayoutParams.TYPE_PHONE,
      WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
      PixelFormat.TRANSLUCENT
    )
    contentParams.gravity = Gravity.TOP or Gravity.START
    contentParams.x = 0
    contentParams.y = 0
    contentView.setOnClickListener {
      switchFloatingView()
    }
    contentView.isFocusable = true
    contentView.isFocusableInTouchMode = true
    contentView.requestFocus()
    contentView.setOnKeyListener { v, keyCode, event ->
      Log.i("contentView onKey: keyCode=$keyCode, event=$event")
      if (event.action == KeyEvent.ACTION_DOWN) {
        when (keyCode) {
          KeyEvent.KEYCODE_BACK -> {
            // Ensure we only switch if contentView is currently the active one
            if (contentView.visibility == View.VISIBLE) {
              switchFloatingView()
            }
            return@setOnKeyListener true
          }
        }
      }
      return@setOnKeyListener false
    }

    setupRecyclerView()
  }


  private fun setupRecyclerView() {
    chatAdapter = ChatAdapter(chatMessages) { message ->
      // 处理语音播放点击
    }
    chatRecyclerView.apply {
      layoutManager = LinearLayoutManager(this@AssistantService).apply {
        stackFromEnd = true
      }
      adapter = chatAdapter
    }
  }


  private fun scrollToBottom() {
    if (chatAdapter.itemCount > 0) {
      chatRecyclerView.smoothScrollToPosition(chatAdapter.itemCount - 1)
    }
  }

  private fun addMessage(message: ChatMessage) {
    chatMessages.add(message)
    chatAdapter.notifyItemInserted(chatAdapter.itemCount - 1)
    scrollToBottom()
  }

  override fun onDestroy() {
    try {
      if (floatingView.isVisible) {
        windowManager.removeView(floatingView)
      } else {
        windowManager.removeView(contentView)
      }
    }catch (_: Exception) { }
    aiServer.release()
    super.onDestroy()
  }

  private fun startForeground() {
    val channelId = "clear_terminal_channel"
    val channelName = "Clear Assistant"

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      val channel = NotificationChannel(
        channelId,
        channelName,
        NotificationManager.IMPORTANCE_LOW
      )
      val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
      manager.createNotificationChannel(channel)
    }

    val notification = NotificationCompat.Builder(this, channelId)
      .setContentTitle("Clear Assistant Service")
      .setContentText("清鹤助手正在运行")
      .setSmallIcon(R.drawable.ic_default)
      .build()

    startForeground(1, notification)
  }

}