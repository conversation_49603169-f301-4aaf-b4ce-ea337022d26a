package cn.cleartv.terminal.assistant

import android.Manifest
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioRecord
import android.media.AudioTrack
import android.media.MediaRecorder
import android.media.audiofx.AcousticEchoCanceler
import android.media.audiofx.AutomaticGainControl
import android.media.audiofx.HapticGenerator
import android.media.audiofx.LoudnessEnhancer
import android.media.audiofx.NoiseSuppressor
import androidx.annotation.RequiresPermission
import cn.cleartv.opus.Opus
import cn.cleartv.terminal.audioManager
import cn.cleartv.terminal.log.Log
import cn.cleartv.terminal.network.NetworkManager
import cn.cleartv.terminal.toJson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import okio.ByteString
import okio.ByteString.Companion.toByteString
import org.json.JSONObject

class AiServer(val wsUrl: String, val terminalId: String) {
  private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
  private var webSocket: WebSocket? = null
  private val client: OkHttpClient by lazy {
    NetworkManager.createOkhttpClient()
  }

  private val codec = Opus()
  private var recordJob: Job? = null

  private var recorder: AudioRecord? = null

  // 音频编码参数
  private var sampleRate = 16000 // 16kHz
  private var channels = 1 // 单声道
  private var frameSize = 960 // 60ms at 16kHz

  //  private var frameSize = 1600 // 100ms at 16kHz
  private var echoCanceller: AcousticEchoCanceler? = null
  private var noiseSuppressor: NoiseSuppressor? = null
  private var automaticGainControl: AutomaticGainControl? = null
  private var loudnessEnhancer: LoudnessEnhancer? = null
  private var hapticGenerator: HapticGenerator? = null

  private val _recordStatusFlow: MutableStateFlow<Boolean> = MutableStateFlow(false)
  val recordStatusFlow: StateFlow<Boolean> = _recordStatusFlow

  private val _playStatusFlow: MutableStateFlow<Boolean> = MutableStateFlow(false)
  val playStatusFlow: StateFlow<Boolean> = _playStatusFlow

  private val _sttSharedFlow: MutableSharedFlow<String> =
    MutableSharedFlow<String>(extraBufferCapacity = 10)
  val sttSharedFlow: SharedFlow<String> = _sttSharedFlow


  private val _ttsStatusFlow: MutableStateFlow<String> = MutableStateFlow("stop")
  val ttsStatusFlow: StateFlow<String> = _ttsStatusFlow
  private val _ttsSharedFlow: MutableSharedFlow<String> =
    MutableSharedFlow<String>(extraBufferCapacity = 10)
  val ttsSharedFlow: SharedFlow<String> = _ttsSharedFlow

  private val helloMessage = mapOf(
    "type" to "hello",
    "version" to 1,
    "features" to mapOf("mcp" to true),
    "transport" to "websocket",
    "audio_params" to mapOf(
      "format" to "opus",
      "sample_rate" to sampleRate,
      "channels" to channels,
      "frame_duration" to frameSize * 1000 / sampleRate,
    )
  )


  private lateinit var track: AudioTrack

  private val isMono = channels == 1
  private val bufferSize = AudioRecord.getMinBufferSize(
    sampleRate,
    if (isMono) AudioFormat.CHANNEL_IN_MONO else AudioFormat.CHANNEL_IN_STEREO,
    AudioFormat.ENCODING_PCM_16BIT
  )

  fun enablePlay(enable: Boolean) {
    if (enable) {
      track.play()
    } else {
      track.stop()
    }
    _playStatusFlow.value = enable
  }

  fun release() {
    codec.encoderRelease()
    codec.decoderRelease()
    disconnect()
  }

  fun init() {
    audioManager.mode = AudioManager.MODE_IN_COMMUNICATION
    audioManager.isSpeakerphoneOn = true

    codec.decoderInit(sampleRate, channels)
    codec.encoderInit(sampleRate, channels, Opus.OPUS_APPLICATION_RESTRICTED_LOWDELAY)

    track = AudioTrack(
//      AudioManager.STREAM_VOICE_CALL,
      MediaRecorder.AudioSource.VOICE_COMMUNICATION, // Changed this line
      sampleRate,
      if (isMono) AudioFormat.CHANNEL_OUT_MONO else AudioFormat.CHANNEL_OUT_STEREO,
      AudioFormat.ENCODING_PCM_16BIT,
      bufferSize,
      AudioTrack.MODE_STREAM
    )
  }

  fun disconnect() {
    webSocket?.close(1000, "Client disconnect")
    webSocket?.cancel()
    webSocket = null
    _ttsStatusFlow.value = "disconnected"
  }
  fun connect() {
    // 建立WebSocket连接
    webSocket = client.newWebSocket(
      Request.Builder()
        .url(wsUrl)
        .header("device-id", terminalId)
        .build(), object : WebSocketListener() {
        override fun onOpen(webSocket: WebSocket, response: Response) {
          <EMAIL> = webSocket
          // 连接成功，可以发送身份认证消息
          Log.i("Connected with terminal ID: $terminalId")
          webSocket.send(helloMessage.toJson())
        }

        override fun onMessage(webSocket: WebSocket, text: String) {
          Log.i("onMessage: $text")
          JSONObject(text).let { json ->
            when (json.getString("type")) {
              "tts" -> {
                val state = json.getString("state")
                _ttsStatusFlow.value = state
                if (state == "sentence_start") {
                  val ttsText = json.optString("text")
                  if (ttsText.isNotBlank()) {
                    _ttsSharedFlow.tryEmit(ttsText)
                  }
                }
              }

              "stt" -> {
                val sttText = json.optString("text")
                if (sttText.isNotBlank()) {
                  _sttSharedFlow.tryEmit(sttText)
                }
              }

              "mcp" -> {

              }

              else -> {
              }
            }
          }
        }

        override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
          // 处理接收到的二进制消息
          playOpusFrame(bytes.toByteArray())
        }

        override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
          // 连接正在关闭
          Log.i("onClosing: $code, $reason")
          webSocket.close(code, reason)
        }

        override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
          // 连接失败
          Log.w("WebSocket connection failed: ${t.message}")
        }

        override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
          // 连接已关闭
          Log.i("onClosed: $code, $reason")
          <EMAIL> = null
        }
      }
    )
  }

  @RequiresPermission(Manifest.permission.RECORD_AUDIO)
  fun startRecording() {
    recordJob?.cancel()
    recordJob = scope.launch {
      webSocket?.send(
        mapOf(
          "type" to "listen",
          "mode" to "manual",
          "state" to "start"
        ).toJson()
      )
      startRecord()
      while (isActive) {
        getOpusFrame(frameSize)?.let { opus ->
          webSocket?.send(opus.toByteString(0, opus.size))
        }
      }
    }
  }

  fun stopRecording() {
    recordJob?.cancel()
    recordJob = null
    stopRecord()
    webSocket?.send(
      mapOf(
        "type" to "listen",
        "mode" to "manual",
        "state" to "stop"
      ).toJson()
    )
  }

  fun sendTextMessage(message: String) {
    Log.i("sendString: $message")
    webSocket?.send(
      mapOf(
        "type" to "listen",
        "mode" to "manual",
        "state" to "detect",
        "text" to message
      ).toJson()
    )
  }

  @RequiresPermission(Manifest.permission.RECORD_AUDIO)
  private fun startRecord() {
    stopRecord()
    try {
      val recorder = AudioRecord(
        MediaRecorder.AudioSource.VOICE_COMMUNICATION,
        sampleRate,
        if (isMono) AudioFormat.CHANNEL_IN_MONO else AudioFormat.CHANNEL_IN_STEREO,
        AudioFormat.ENCODING_PCM_16BIT,
        bufferSize
      )

      if(AcousticEchoCanceler.isAvailable()){
        try {
          echoCanceller = AcousticEchoCanceler.create(recorder.audioSessionId)
          echoCanceller?.enabled = true
          Log.i("echoCanceller enabled")
        } catch (e: Exception) {
          Log.e("[initRecorder] unable to init echo canceller: $e")
        }
      }

      if (NoiseSuppressor.isAvailable()) {
        try {
          noiseSuppressor = NoiseSuppressor.create(recorder.audioSessionId)
          noiseSuppressor?.enabled = true
          Log.i("noiseSuppressor enabled")
        } catch (e: Exception) {
          Log.e("[initRecorder] unable to init noise suppressor: $e")
        }
      }

      if (AutomaticGainControl.isAvailable()) {
        try {
          automaticGainControl =
            AutomaticGainControl.create(recorder.audioSessionId)
          automaticGainControl?.enabled = true
          Log.i("automaticGainControl enabled")
        } catch (e: Exception) {
          Log.e("[initRecorder] unable to init automatic gain control: $e")
        }
      }
      this.recorder = recorder
      recorder.startRecording()
      _recordStatusFlow.value = true
    } catch (e: Exception) {
      _recordStatusFlow.value = false
      Log.e("[initRecorder] error: $e")
    }
  }

  private fun stopRecord() {
    try {
      if (recorder?.state == AudioTrack.STATE_INITIALIZED) recorder?.stop()
      recorder?.release()
      echoCanceller?.release()
      noiseSuppressor?.release()
      automaticGainControl?.release()
      hapticGenerator?.release()
      loudnessEnhancer?.release()
    } catch (e: Exception) {
      Log.e("[stopRecord] error: $e")
    }
    _recordStatusFlow.value = false
  }

  fun getPcmFrame(frameSize: Int): ByteArray? {
    return recorder?.let { recorder ->
      val frame = ByteArray(frameSize)
      var offset = 0
      var remained = frame.size
      while (remained > 0) {
        val read = recorder.read(frame, offset, remained)
        offset += read
        remained -= read
      }
      frame
    }
  }

  fun getOpusFrame(frameSize: Int): ByteArray? {
    return recorder?.let { recorder ->
      val frame = ByteArray(frameSize * 2)
      var offset = 0
      var remained = frame.size
      while (remained > 0) {
        val read = recorder.read(frame, offset, remained)
        offset += read
        remained -= read
      }
      codec.encode(frame, frameSize)
    }
  }

  fun playOpusFrame(frame: ByteArray) {
    codec.decode(frame, frameSize)?.let { pcm ->
      track.write(pcm, 0, pcm.size)
    }
  }

}