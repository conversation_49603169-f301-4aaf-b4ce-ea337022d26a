package cn.cleartv.terminal.assistant.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.cleartv.terminal.R
import cn.cleartv.terminal.assistant.model.ChatMessage
import cn.cleartv.terminal.assistant.model.MessageType
import java.text.SimpleDateFormat
import java.util.*

class ChatAdapter(
    private val messages: List<ChatMessage>,
    private val onVoicePlayClick: (ChatMessage) -> Unit = {}
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_USER = 1
        private const val VIEW_TYPE_AI = 2
    }

    private val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    
    override fun getItemViewType(position: Int): Int {
        return if (messages[position].isUser) VIEW_TYPE_USER else VIEW_TYPE_AI
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_USER -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_chat_user, parent, false)
                UserMessageViewHolder(view)
            }
            else -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_chat_ai, parent, false)
                AiMessageViewHolder(view)
            }
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val message = messages[position]
        when (holder) {
            is UserMessageViewHolder -> holder.bind(message)
            is AiMessageViewHolder -> holder.bind(message)
        }
    }
    
    override fun getItemCount(): Int = messages.size
    
    inner class UserMessageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val messageText: TextView = itemView.findViewById(R.id.messageText)
        private val timeText: TextView = itemView.findViewById(R.id.timeText)
        private val voicePlayButton: ImageButton? = itemView.findViewById(R.id.voicePlayButton)

        fun bind(message: ChatMessage) {
            when (message.messageType) {
                MessageType.TEXT -> {
                    messageText.visibility = View.VISIBLE
                    voicePlayButton?.visibility = View.GONE
                    messageText.text = message.text
                }
                MessageType.VOICE -> {
                    messageText.visibility = View.GONE
                    voicePlayButton?.visibility = View.VISIBLE
                    voicePlayButton?.setOnClickListener {
                        onVoicePlayClick(message)
                    }
                }
            }
            timeText.text = timeFormat.format(Date(message.timestamp))
        }
    }
    
    inner class AiMessageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val messageText: TextView = itemView.findViewById(R.id.messageText)
        private val timeText: TextView = itemView.findViewById(R.id.timeText)
        private val voicePlayButton: ImageButton? = itemView.findViewById(R.id.voicePlayButton)

        fun bind(message: ChatMessage) {
            when (message.messageType) {
                MessageType.TEXT -> {
                    messageText.visibility = View.VISIBLE
                    voicePlayButton?.visibility = View.GONE
                    messageText.text = message.text
                }
                MessageType.VOICE -> {
                    messageText.visibility = View.GONE
                    voicePlayButton?.visibility = View.VISIBLE
                    voicePlayButton?.setOnClickListener {
                        onVoicePlayClick(message)
                    }
                }
            }

            timeText.text = if (message.isTyping) "" else timeFormat.format(Date(message.timestamp))

            // 如果是正在输入状态，可以添加动画效果
            if (message.isTyping) {
                messageText.alpha = 0.7f
            } else {
                messageText.alpha = 1.0f
            }
        }
    }
}