package cn.cleartv.terminal

import android.util.DisplayMetrics
import cn.cleartv.terminal.utils.DeviceUtils
import cn.cleartv.terminal.utils.NetworkUtils

object DeviceInfo {

  val id: String by lazy { DeviceUtils.getUniqueId() }
  val mac: String by lazy { DeviceUtils.getMacAddress() }
  val ip: String get() = NetworkUtils.getIPAddress()
  val displayMetrics: List<DisplayMetrics>
    get() = displayManager.displays.map {
      DisplayMetrics().apply {
        it.getRealMetrics(this)
      }
    }.toList()
  val cpuUsed: Float get() = DeviceUtils.getCpuUsed()
  val usedMemory: Long get() = DeviceUtils.getUsedMemory()
  val totalMemory: Long get() = DeviceUtils.getTotalMemory()
  val availableMemory: Long get() = DeviceUtils.getAvailMemory()
  val totalInternalDiskSize: Long get() = DeviceUtils.getTotalInternalMemorySize()
  val availableInternalDiskSize: Long get() = DeviceUtils.getAvailableInternalMemorySize()
  val totalExternalDiskSize: Long get() = DeviceUtils.getTotalExternalMemorySize()
  val availableExternalDiskSize: Long get() = DeviceUtils.getAvailableExternalMemorySize()
}