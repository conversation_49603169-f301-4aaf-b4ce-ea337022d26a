package cn.cleartv.terminal.ui

import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.text.InputType
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import cn.cleartv.terminal.R
import cn.cleartv.terminal.launchApp
import cn.cleartv.terminal.launchFileView
import cn.cleartv.terminal.launchSetting
import cn.cleartv.terminal.launchWeb
import cn.cleartv.terminal.showToast
import cn.cleartv.terminal.ui.dialog.TextInputDialog
import cn.cleartv.terminal.utils.DeviceUtils
import cn.cleartv.terminal.utils.NetworkUtils
import cn.cleartv.terminal.utils.TimeUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

class HomeActivity : AppCompatActivity(R.layout.activity_home) {

  private lateinit var rvFunctionList: RecyclerView
  private lateinit var tvIP: TextView
  private lateinit var tvMAC: TextView
  private lateinit var tvDateTime: TextView
  private lateinit var tvVersion: TextView
  private lateinit var gridLayoutManager: GridLayoutManager
  private lateinit var functionAdapter: FunctionAdapter
  private val functionList = listOf(
    FunctionItem(1, "Web"),
    FunctionItem(2, "App"),
    FunctionItem(3, "File"),
    FunctionItem(4, "Setting"),
  )

  private lateinit var filePickerLauncher: ActivityResultLauncher<Array<String>>

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    initViews()
    setupRecyclerView()
    updateDateTime()
    updateVersionInfo()
    updateNetworkInfo()

    lifecycleScope.launch {
      repeatOnLifecycle(Lifecycle.State.RESUMED) {
        while (isActive) {
          delay(1000) // 每秒更新一次
          updateDateTime()
        }
      }
    }

    NetworkUtils.registerNetworkStatusChangedListener(object :
      NetworkUtils.OnNetworkStatusChangedListener {
      override fun onDisconnected() {
        updateNetworkInfo()
      }

      override fun onConnected(networkType: NetworkUtils.NetworkType) {
        updateNetworkInfo()
      }

    })

    filePickerLauncher =
      registerForActivityResult<Array<String>, Uri?>(ActivityResultContracts.OpenDocument()) { uri: Uri? ->
        uri?.let {
          // Ensure launchFileView can handle content URIs
          launchFileView(it)
        } ?: run {
          showToast("No file selected")
        }
      }
  }

  private fun initViews() {
    rvFunctionList = findViewById(R.id.rvFunctionList)
    tvIP = findViewById(R.id.tvIP)
    tvMAC = findViewById(R.id.tvMAC)
    tvDateTime = findViewById(R.id.tvDateTime)
    tvVersion = findViewById(R.id.tvVersion)
  }

  private fun updateVersionInfo() {
    try {
      val packageInfo = packageManager.getPackageInfo(packageName, 0)
      val versionName = packageInfo.versionName
      val versionCode = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
        packageInfo.longVersionCode
      } else {
        @Suppress("DEPRECATION")
        packageInfo.versionCode.toLong()
      }
      tvVersion.text = "版本号: v$versionName ($versionCode)"
    } catch (e: Exception) {
      tvVersion.text = "版本号: 未知"
    }
  }

  private fun updateDateTime() {
    tvDateTime.text = TimeUtils.nowString
  }

  private fun updateNetworkInfo() {
    // 获取IP地址
    val ipAddress = NetworkUtils.getIPAddress(true)
    tvIP.text = "IP: $ipAddress"

    // 获取MAC地址（在新版本Android中可能无法获取真实MAC地址）
    val macAddress = DeviceUtils.getMacAddress()
    tvMAC.text = "MAC: $macAddress"
  }

  private fun setupRecyclerView() {
    gridLayoutManager = GridLayoutManager(this, getSpanCount())
    functionAdapter = FunctionAdapter(functionList.sortedBy { it.id }) { functionItem ->
      onFunctionItemClick(functionItem)
    }

    rvFunctionList.apply {
      layoutManager = gridLayoutManager
      adapter = functionAdapter
    }
  }

  override fun onConfigurationChanged(newConfig: Configuration) {
    super.onConfigurationChanged(newConfig)
    gridLayoutManager.spanCount = getSpanCount()
  }

  private fun getSpanCount(): Int {
    return when (resources.configuration.orientation) {
      Configuration.ORIENTATION_LANDSCAPE -> 4
      else -> 2
    }
  }

  private fun onFunctionItemClick(functionItem: FunctionItem) {
    when (functionItem.name) {
      "Web" -> {
        TextInputDialog.Builder(this)
          .setTitle("输入访问的Url地址")
          .setHint("输入访问的Url地址")
          .setInitialValue("")
          .setInputType(InputType.TYPE_TEXT_VARIATION_URI)
          .setOnConfirmListener { inputText ->
            launchWeb(inputText)
          }
          .show()
      }

      "App" -> {
        TextInputDialog.Builder(this)
          .setTitle("输入打开应用的包名或Activity路径")
          .setHint("输入打开应用的包名或Activity路径")
          .setInitialValue("")
          .setInputType(InputType.TYPE_TEXT_VARIATION_URI)
          .setOnConfirmListener { inputText ->
            launchApp(inputText)
          }
          .show()
      }

      "File" -> {
        showFilePickerDialog()
      }

      "Setting" -> {
        launchSetting()
      }
    }
  }

  private fun showFilePickerDialog() {
    val mimeTypes = arrayOf("*/*")
    filePickerLauncher.launch(mimeTypes)
  }

}

data class FunctionItem(
  val id: Int,
  val name: String,
  val iconResId: Int? = null
)

class FunctionAdapter(
  private val functionList: List<FunctionItem>,
  private val onItemClick: (FunctionItem) -> Unit
) : RecyclerView.Adapter<FunctionAdapter.FunctionViewHolder>() {

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FunctionViewHolder {
    val view = LayoutInflater.from(parent.context)
      .inflate(R.layout.item_function, parent, false)
    return FunctionViewHolder(view)
  }

  override fun onBindViewHolder(holder: FunctionViewHolder, position: Int) {
    holder.bind(functionList[position])
  }

  override fun getItemCount(): Int = functionList.size

  inner class FunctionViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
    private val iconImageView: ImageView = itemView.findViewById(R.id.ivFunctionIcon)
    private val nameTextView: TextView = itemView.findViewById(R.id.tvFunctionName)

    fun bind(functionItem: FunctionItem) {
      try {
        val iconResId = functionItem.iconResId ?: when (functionItem.name) {
          "Web" -> R.drawable.ic_web
          "App" -> R.drawable.ic_app
          "File" -> R.drawable.ic_file
          "Setting" -> R.drawable.ic_setting
          else -> R.drawable.ic_default
        }
        iconImageView.setImageDrawable(ContextCompat.getDrawable(itemView.context, iconResId))
      } catch (e: Exception) {
        iconImageView.setImageResource(R.drawable.ic_default)
      }
      nameTextView.text = functionItem.name
      itemView.setOnClickListener {
        onItemClick(functionItem)
      }
    }
  }
}
