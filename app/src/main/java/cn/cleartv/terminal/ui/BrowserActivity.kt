package cn.cleartv.terminal.ui

import android.Manifest
import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.edit
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import cn.cleartv.terminal.App
import cn.cleartv.terminal.Constants
import cn.cleartv.terminal.R
import cn.cleartv.terminal.enableFullScreen
import cn.cleartv.terminal.hasPermission
import cn.cleartv.terminal.lockLandscapeOrientation
import cn.cleartv.terminal.lockPortraitOrientation
import cn.cleartv.terminal.log.Log
import cn.cleartv.terminal.showToast
import com.google.android.material.snackbar.Snackbar
import com.tencent.smtt.export.external.extension.proxy.ProxyWebChromeClientExtension
import com.tencent.smtt.export.external.extension.proxy.ProxyWebViewClientExtension
import com.tencent.smtt.export.external.interfaces.GeolocationPermissionsCallback
import com.tencent.smtt.export.external.interfaces.IX5WebChromeClient
import com.tencent.smtt.export.external.interfaces.MediaAccessPermissionsCallback
import com.tencent.smtt.sdk.QbSdk
import com.tencent.smtt.sdk.ValueCallback
import com.tencent.smtt.sdk.WebChromeClient
import com.tencent.smtt.sdk.WebSettings
import com.tencent.smtt.sdk.WebView
import com.tencent.smtt.sdk.WebViewClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.net.URLDecoder
import java.nio.charset.StandardCharsets


@Suppress("DEPRECATION")
open class BrowserActivity : AppCompatActivity() {

  private var fileCallback: android.webkit.ValueCallback<Uri>? = null
  private var filesCallback: android.webkit.ValueCallback<Array<Uri?>>? = null
  private var permissionOrigin: String? = null
  private var audioPermissionsCallback: MediaAccessPermissionsCallback? = null
  private var cameraPermissionsCallback: MediaAccessPermissionsCallback? = null
  private var mediaPermissionsCallback: MediaAccessPermissionsCallback? = null

  private val sp: SharedPreferences by lazy {
    getSharedPreferences("browser_config", MODE_PRIVATE)
  }

  val webView: WebView by lazy {
    // 耗时较长
    WebView(this).apply {
//      setBackgroundColor(resources.getColor(R.color.main_background))
//      background
    }
  }

  private lateinit var titleBar: View
  private lateinit var logoIconView: ImageView
  private lateinit var titleTextView: TextView
  private lateinit var urlEditText: EditText
  private lateinit var menuIconView: View
  private lateinit var swipeRefreshLayout: SwipeRefreshLayout
  private lateinit var webContentView: FrameLayout
  private lateinit var progressBar: ProgressBar


  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    Log.i("onCreate")
    enableFullScreen()
    setContentView(R.layout.activity_browser)
    titleBar = findViewById(R.id.title_bar)
    logoIconView = findViewById(R.id.logo_icon)
    titleTextView = findViewById(R.id.title)
    urlEditText = findViewById(R.id.url)
    menuIconView = findViewById(R.id.menu_icon)
    swipeRefreshLayout = findViewById(R.id.swipe_refresh_layout)
    webContentView = findViewById(R.id.web_content)
    progressBar = findViewById(R.id.progress_bar)


    swipeRefreshLayout.setOnRefreshListener {
      webView.reload()
    }

    swipeRefreshLayout.setOnChildScrollUpCallback { parent, child ->
      return@setOnChildScrollUpCallback webView.webScrollY > 0
    }
    swipeRefreshLayout.isEnabled = false
    urlEditText.setOnEditorActionListener { v, actionId, event ->
      when (actionId) {
        EditorInfo.IME_ACTION_GO -> {
          val url = urlEditText.text.toString()
          sp.edit { putString("hostUrl", url) }
          loadUrl(url)
          urlEditText.visibility = View.GONE
          titleTextView.visibility = View.VISIBLE
        }
      }
      return@setOnEditorActionListener false
    }
    menuIconView.setOnClickListener {
      if (urlEditText.isVisible) {
        urlEditText.visibility = View.GONE
        titleTextView.visibility = View.VISIBLE
      } else {
        urlEditText.visibility = View.VISIBLE
        titleTextView.visibility = View.GONE
      }
    }
    titleBar.visibility = View.GONE


    val defaultUrl = "https://bing.com"
//    val defaultUrl = "http://127.0.0.1:3000/frontend"
//    val defaultUrl = "https://debugtbs.qq.com/"
//    val defaultUrl = "http://soft.imtt.qq.com/browser/tes/feedback.html"
//    val defaultUrl = "http://soft.tbs.imtt.qq.com/17421/tbs_res_imtt_qqbrowser_x5_res_bradywwang_core_load_check.pdf"
    val url = intent.getStringExtra("url") ?: sp.getString("hostUrl", defaultUrl) ?: defaultUrl
    urlEditText.setText(url)

    var cacheMode = WebSettings.LOAD_DEFAULT
    try {
      url.toUri().getQueryParameter("cacheMode")?.toIntOrNull()?.let { cacheMode = it }
    } catch (_: Exception) {
    }
    lifecycleScope.launch(Dispatchers.IO) {
      Log.i("TBS：getTbsSdkVersion: ${QbSdk.getTbsSdkVersion()}")
      Log.i("TBS：getTbsVersion: ${QbSdk.getTbsVersion(App.instance)}")
      Log.i("TBS：canLoadX5: ${QbSdk.canLoadX5(App.instance)}")
      val x5WebViewExtension = webView.x5WebViewExtension
      Log.i("TBS：isTbsActive: ${x5WebViewExtension?.isActive == true}")
      x5WebViewExtension?.apply {
        Log.i("TBS：qqBrowserVersion: $qqBrowserVersion")
      }
      withContext(Dispatchers.Main) {
        initWebView(cacheMode)
        loadUrl(url)
      }
    }
  }

  @SuppressLint("SetJavaScriptEnabled")
  fun initWebView(cacheMode: Int) {
    Log.i("init webView, cacheMode: $cacheMode")
    webContentView.addView(webView)
    webView.settings.apply {
      // 支持屏幕缩放
      setSupportZoom(true)
      // 支持内置缩放控件
      builtInZoomControls = true
      // 不显示缩放按钮
      displayZoomControls = false
      // 自适应宽度
      useWideViewPort = true
      loadWithOverviewMode = true
      layoutAlgorithm = WebSettings.LayoutAlgorithm.NORMAL
      setAppCacheEnabled(true)
//      cacheMode = WebSettings.LOAD_NORMAL
      this.cacheMode = cacheMode
      // 允许Android调用JS
      domStorageEnabled = true
      javaScriptEnabled = true
      // 设置WebView对象的编码格式为UTF_8
      defaultTextEncodingName = "utf-8"
      blockNetworkImage = false
      // 自动加载图片
      loadsImagesAutomatically = true

      allowFileAccess = true

      setGeolocationEnabled(true)
    }

    // todo 添加JS接口
//    webView.addJavascriptInterface(ClearLauncherJS(), "clear_launcher")
//    webView.addJavascriptInterface(RtcPluginJS(), "rtc_plugin")

    webView.setDownloadListener { url, userAgent, contentDisposition, minetype, contentLength ->
      // todo 实现下载功能
      Log.i("url=$url")
      Log.i("userAgent=$userAgent")
      Log.i("contentDisposition=$contentDisposition")
      Log.i("mimetype=$minetype")
      Log.i("contentLength=$contentLength")
      val uri = url.toUri()
      val intent = Intent(Intent.ACTION_VIEW, uri)
      startActivity(intent)
    }
    webView.webViewClient = object : WebViewClient() {

      override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
        return loadUrl(url)
      }

      override fun onPageFinished(view: WebView, url: String) {
        pageLoadFinished()
      }

    }
    webView.webChromeClient = object : WebChromeClient() {

      private var mCustomView: View? = null
      private var mCustomViewCallback: IX5WebChromeClient.CustomViewCallback? = null
      private var mOriginalOrientation = 0
      private var mOriginalSystemUiVisibility = 0

      override fun getDefaultVideoPoster(): Bitmap? {
        if (mCustomView == null) {
          return null
        }
        return BitmapFactory.decodeResource(applicationContext.resources, 2130837573);
      }

      override fun onHideCustomView() {
        (window.decorView as FrameLayout).removeView(this.mCustomView)
        this.mCustomView = null
        window.decorView.systemUiVisibility = mOriginalSystemUiVisibility
        requestedOrientation = mOriginalOrientation
        mCustomViewCallback?.onCustomViewHidden()
        this.mCustomViewCallback = null
        lockPortraitOrientation()
      }

      override fun onShowCustomView(
        paramView: View?,
        paramCustomViewCallback: IX5WebChromeClient.CustomViewCallback?
      ) {
        if (this.mCustomView != null) {
          onHideCustomView()
          return
        }
        this.mCustomView = paramView
        this.mOriginalSystemUiVisibility = window.decorView.systemUiVisibility
        this.mOriginalOrientation = requestedOrientation
        this.mCustomViewCallback = paramCustomViewCallback
        (window.decorView as FrameLayout).addView(
          this.mCustomView,
          FrameLayout.LayoutParams(-1, -1)
        )
        window.decorView.systemUiVisibility = 3846 or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
        lockLandscapeOrientation()
      }

      override fun onProgressChanged(view: WebView, progress: Int) {
        if (progress in 1..99) {
          progressBar.visibility = View.VISIBLE
          progressBar.progress = progress
        } else {
          progressBar.visibility = View.GONE
          progressBar.progress = 100
        }
      }

      override fun onReceivedTitle(webView: WebView, title: String?) {
        titleTextView.text = title
      }

      override fun onReceivedIcon(webView: WebView, bitmap: Bitmap?) {
        logoIconView.visibility = if (bitmap == null) View.GONE else View.VISIBLE
        logoIconView.setImageBitmap(bitmap)
      }

      override fun openFileChooser(
        callback: ValueCallback<Uri>,
        acceptType: String,
        captureType: String
      ) {
        fileCallback = callback
        openFileChooseProcess(false)
      }

      override fun onShowFileChooser(
        webView: WebView,
        filePathCallback: ValueCallback<Array<Uri?>>,
        fileChooserParams: FileChooserParams
      ): Boolean {
        filesCallback = filePathCallback
        openFileChooseProcess(true)
        return true
      }

      override fun onGeolocationPermissionsShowPrompt(
        origin: String?,
        callback: GeolocationPermissionsCallback?
      ) {
        Log.i("onGeolocationPermissionsShowPrompt: $origin")
        super.onGeolocationPermissionsShowPrompt(origin, callback)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
          if (!hasPermission(Manifest.permission.ACCESS_COARSE_LOCATION)
            || !hasPermission(Manifest.permission.ACCESS_FINE_LOCATION)
          ) {
            requestPermissions(
              arrayOf(
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.ACCESS_FINE_LOCATION,
              ), Constants.RequestCode.REQUEST_LOCATION
            )
          }
        }
      }

      override fun onGeolocationPermissionsHidePrompt() {
        super.onGeolocationPermissionsHidePrompt()
        Log.i("onGeolocationPermissionsHidePrompt")
      }
    }
    webView.webViewClientExtension = object : ProxyWebViewClientExtension() {

    }
    webView.webChromeClientExtension = object : ProxyWebChromeClientExtension() {

      override fun onBackforwardFinished(i: Int) {
        pageLoadFinished()
      }

      override fun openFileChooser(
        callback: android.webkit.ValueCallback<Array<Uri?>>,
        acceptType: String?,
        captureType: String?
      ) {
        filesCallback = callback
        openFileChooseProcess(true)
      }

      override fun onPermissionRequest(
        s: String,
        l: Long,
        callback: MediaAccessPermissionsCallback
      ): Boolean {
        Log.i("onPermissionRequest：$s ; $l")
        when (l) {
          MediaAccessPermissionsCallback.ALLOW_AUDIO_CAPTURE -> {
            if (hasPermission(Manifest.permission.RECORD_AUDIO) || Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
              callback.invoke(s, l, true)
            } else {
              permissionOrigin = s
              audioPermissionsCallback = callback
              requestPermissions(
                arrayOf(Manifest.permission.RECORD_AUDIO),
                Constants.RequestCode.REQUEST_AUDIO_PERMISSION
              )
            }
          }

          MediaAccessPermissionsCallback.ALLOW_VIDEO_CAPTURE -> {
            if (hasPermission(Manifest.permission.CAMERA) || Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
              callback.invoke(s, l, true)
            } else {
              permissionOrigin = s
              cameraPermissionsCallback = callback
              requestPermissions(
                arrayOf(Manifest.permission.CAMERA),
                Constants.RequestCode.REQUEST_CAMERA_PERMISSION
              )
            }
          }

          MediaAccessPermissionsCallback.ALLOW_AUDIO_CAPTURE or MediaAccessPermissionsCallback.ALLOW_VIDEO_CAPTURE -> {
            if (hasPermission(Manifest.permission.CAMERA) && hasPermission(Manifest.permission.RECORD_AUDIO) || Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
              callback.invoke(s, l, true)
            } else {
              permissionOrigin = s
              mediaPermissionsCallback = callback
              requestPermissions(
                arrayOf(
                  Manifest.permission.CAMERA,
                  Manifest.permission.RECORD_AUDIO
                ), Constants.RequestCode.REQUEST_MEDIA_PERMISSION
              )
            }
          }
        }
        return true
      }
    }
  }

  private fun pageLoadFinished() {
    progressBar.visibility = View.GONE
    swipeRefreshLayout.isRefreshing = false
  }

  fun loadUrl(url: String): Boolean {
    val uri = url.toUri()
    when (uri.scheme) {
      "function" -> {
        when (uri.authority) {
          "updateHostUrl" -> {
            val hostUrl =
              URLDecoder.decode(uri.getQueryParameter("hostUrl"), StandardCharsets.UTF_8.name())
            urlEditText.setText(url)
            sp.edit { putString("hostUrl", hostUrl) }
            loadUrl(hostUrl)
          }
        }
        return true
      }

      "intent" -> {
        Log.i("加载Intent：$url")
        val intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME)
        intent.addCategory(Intent.CATEGORY_BROWSABLE)
        startActivity(intent)
      }

      "http", "https" -> {
        Log.i("加载网页：$url")
        webView.loadUrl(url)
        return false
      }

      "qqmap" -> {
        Log.i("打开腾讯地图：$url")
        if (isAppAvailable(Constants.MapAppPackageName.TENCENT)) {
          startUrlActivity(url)
        } else {
          Snackbar.make(webView, "未安装腾讯地图", Snackbar.LENGTH_LONG).setAction(
            "去应用市场下载"
          ) { openAppMarket(Constants.MapAppPackageName.TENCENT) }.show()
        }
      }

      "baidumap" -> {
        Log.i("打开百度地图：$url")
        if (isAppAvailable(Constants.MapAppPackageName.BAIDU)) {
          startUrlActivity(url)
        } else {
          Snackbar.make(webView, "未安装百度地图", Snackbar.LENGTH_LONG).setAction(
            "去应用市场下载"
          ) { openAppMarket(Constants.MapAppPackageName.BAIDU) }.show()
        }
      }

      "androidamap" -> {
        Log.i("打开高德地图：$url")
        if (isAppAvailable(Constants.MapAppPackageName.GAODE)) {
          startUrlActivity(url)
        } else {
          Snackbar.make(webView, "未安装高德地图", Snackbar.LENGTH_LONG).setAction(
            "去应用市场下载"
          ) { openAppMarket(Constants.MapAppPackageName.GAODE) }.show()
        }
      }

      else -> {
        Log.i("其他：$url")
        startUrlActivity(url)
      }
    }
    return true
  }

  @SuppressLint("SdCardPath")
  private fun isAppAvailable(packageName: String): Boolean {
    return File("/data/data/$packageName").exists()
  }

  private fun openAppMarket(packageName: String) {
    val uri = "market://details?id=$packageName".toUri()
    val intent = Intent(Intent.ACTION_VIEW, uri)
    startActivity(intent)
  }

  private fun startUrlActivity(url: String) {
    Log.i("startUrlActivity：$url")
    val i = Intent(Intent.ACTION_VIEW, url.toUri())
    i.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
    try {
      startActivity(i)
    } catch (_: ActivityNotFoundException) {
      showToast("未安装软件")
    }
  }

  private fun openFileChooseProcess(allowMultiple: Boolean) {
    val i = Intent(Intent.ACTION_GET_CONTENT)
    i.addCategory(Intent.CATEGORY_OPENABLE)
    i.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, allowMultiple)
    i.type = "*/*"
    startActivityForResult(
      Intent.createChooser(i, "文件选择"),
      Constants.RequestCode.REQUEST_CHOOSE_FILE
    )
  }

  override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    super.onActivityResult(requestCode, resultCode, data)
    Log.i(
      "onActivityResult：$requestCode ; $resultCode ; $data"
    )
    when (requestCode) {
      Constants.RequestCode.REQUEST_CHOOSE_FILE -> {
        if (resultCode == RESULT_OK) {
          val result = data?.data
          val clipData = data?.clipData
          val results = Array(clipData?.itemCount ?: 1) { i ->
            clipData?.getItemAt(i)?.uri ?: result
          }
          fileCallback?.onReceiveValue(result)
          fileCallback = null
          filesCallback?.onReceiveValue(results)
          fileCallback = null
        } else {
          fileCallback?.onReceiveValue(null)
          fileCallback = null
          filesCallback?.onReceiveValue(null)
          fileCallback = null
        }
      }
    }
  }


  override fun onRequestPermissionsResult(
    requestCode: Int,
    permissions: Array<out String>,
    grantResults: IntArray
  ) {
    super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    Log.i(
      "onRequestPermissionsResult：$requestCode ; ${permissions.contentToString()} ; ${grantResults.contentToString()}"
    )
    when (requestCode) {
      Constants.RequestCode.REQUEST_MEDIA_PERMISSION -> {
        if (grantResults.firstOrNull() == PackageManager.PERMISSION_GRANTED &&
          grantResults.lastOrNull() == PackageManager.PERMISSION_GRANTED
        ) {
          mediaPermissionsCallback?.invoke(
            permissionOrigin,
            MediaAccessPermissionsCallback.ALLOW_AUDIO_CAPTURE or MediaAccessPermissionsCallback.ALLOW_VIDEO_CAPTURE,
            true
          )
        } else {
          mediaPermissionsCallback?.invoke(
            permissionOrigin,
            MediaAccessPermissionsCallback.ALLOW_AUDIO_CAPTURE or MediaAccessPermissionsCallback.ALLOW_VIDEO_CAPTURE,
            false
          )
        }
        mediaPermissionsCallback = null
      }

      Constants.RequestCode.REQUEST_AUDIO_PERMISSION -> {
        if (grantResults.firstOrNull() == PackageManager.PERMISSION_GRANTED) {
          audioPermissionsCallback?.invoke(
            permissionOrigin,
            MediaAccessPermissionsCallback.ALLOW_AUDIO_CAPTURE,
            true
          )
        } else {
          audioPermissionsCallback?.invoke(
            permissionOrigin,
            MediaAccessPermissionsCallback.ALLOW_AUDIO_CAPTURE,
            false
          )
        }
        audioPermissionsCallback = null
      }

      Constants.RequestCode.REQUEST_CAMERA_PERMISSION -> {
        if (grantResults.firstOrNull() == PackageManager.PERMISSION_GRANTED) {
          cameraPermissionsCallback?.invoke(
            permissionOrigin,
            MediaAccessPermissionsCallback.ALLOW_VIDEO_CAPTURE,
            true
          )
        } else {
          cameraPermissionsCallback?.invoke(
            permissionOrigin,
            MediaAccessPermissionsCallback.ALLOW_VIDEO_CAPTURE,
            false
          )
        }
        cameraPermissionsCallback = null
      }
    }
  }


  override fun onResume() {
    super.onResume()
    webView.onResume()
  }

  override fun onPause() {
    super.onPause()
    webView.onPause()
  }

  override fun onDestroy() {
    webView.stopLoading()
    webView.onPause()
    webView.clearCache(true)
    webView.clearHistory()
    webView.removeAllViews()
    webView.destroyDrawingCache()
    webContentView.removeView(webView)
    webView.destroy()
    super.onDestroy()
  }

  override fun onSaveInstanceState(outState: Bundle) {
    super.onSaveInstanceState(outState)
    webView.saveState(outState)
  }

  override fun onRestoreInstanceState(savedInstanceState: Bundle) {
    super.onRestoreInstanceState(savedInstanceState)
    webView.restoreState(savedInstanceState)
  }

  private var mLastBackTime = 0L

  @Deprecated("Deprecated in Java")
  @SuppressLint("MissingSuperCall", "GestureBackNavigation")
  override fun onBackPressed() {
    if (webView.canGoBack()) {
      webView.goBack()
    } else if (System.currentTimeMillis() - mLastBackTime > 1500) {
      showToast("再次点击返回退出")
      mLastBackTime = System.currentTimeMillis()
    } else {
      finish()
    }
  }

}