package cn.cleartv.terminal.ui

import android.os.Bundle
import androidx.preference.PreferenceFragmentCompat
import cn.cleartv.terminal.R
import cn.cleartv.terminal.configPreferenceAdapter

class SettingFragment : PreferenceFragmentCompat() {

  override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
    preferenceManager.preferenceDataStore = configPreferenceAdapter
    setPreferencesFromResource(R.xml.setting_preferences, rootKey)
  }

}