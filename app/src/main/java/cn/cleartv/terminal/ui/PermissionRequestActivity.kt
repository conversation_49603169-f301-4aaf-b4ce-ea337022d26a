package cn.cleartv.terminal.ui

import android.Manifest
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import cn.cleartv.terminal.assistant.AssistantService

class PermissionRequestActivity : AppCompatActivity() {

  companion object {
    const val ACTION_PERMISSIONS_RESULT = "cn.cleartv.terminal.assistant.ACTION_PERMISSIONS_RESULT"
    const val EXTRA_PERMISSIONS = "cn.cleartv.terminal.assistant.EXTRA_PERMISSIONS"
    const val EXTRA_PERMISSIONS_GRANTED = "cn.cleartv.terminal.assistant.EXTRA_PERMISSIONS_GRANTED"
    private const val PERMISSION_REQUEST_CODE = 1

    fun Context.requestPermissions(permissions: Array<String>) {
      val intent = Intent(this, PermissionRequestActivity::class.java)
      intent.putExtra("permissions", permissions)
      intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      this.startActivity(intent)
    }

    fun Context.startOverlayService(startServiceCls: Class<out Service>) {
      val intent = Intent(this, PermissionRequestActivity::class.java)
      intent.putExtra("is_overlay_permission_request", true)
      intent.putExtra("service_class", startServiceCls)
      intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      this.startActivity(intent)
    }
  }

  private lateinit var permissions: Array<String>
  private var serviceClass: Class<out Service>? = null

  private val overlayPermissionLauncher =
    registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        if (Settings.canDrawOverlays(this)) {
          // Permission granted, you can now start your service
          hasOverlayPermission()
        } else {
          // Permission denied
          // You might want to show a dialog explaining why you need the permission
          // and guide the user to settings.
        }
      }
      finish()
    }

  fun checkAndRequestOverlayPermission() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
      if (!Settings.canDrawOverlays(this)) {
        val intent = Intent(
          Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
          Uri.parse("package:$packageName")
        )
        overlayPermissionLauncher.launch(intent)
        return
      } else {
        // Permission already granted
        hasOverlayPermission()
      }
    } else {
      // For versions below Android M, permission is granted at install time
      // if declared in the manifest.
      hasOverlayPermission()
    }
    finish()
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    // You can set a transparent theme for this activity in AndroidManifest.xml
    // e.g., android:theme="@android:style/Theme.Translucent.NoTitleBar"
    // so it doesn't show a UI.

    if (intent.getBooleanExtra("is_overlay_permission_request", false)) {
      serviceClass = intent.getSerializableExtra("service_class") as? Class<out Service>
      checkAndRequestOverlayPermission()
      return
    }

    permissions =
      intent.getStringArrayExtra("permissions") ?: arrayOf(Manifest.permission.RECORD_AUDIO)
    if (savedInstanceState == null) { // Only request if not restoring from a saved state
      ActivityCompat.requestPermissions(
        this,
        permissions,
        PERMISSION_REQUEST_CODE
      )
    }
  }

  override fun onRequestPermissionsResult(
    requestCode: Int,
    permissions: Array<out String>,
    grantResults: IntArray
  ) {
    super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    if (requestCode == PERMISSION_REQUEST_CODE) {
      sendPermissionResult(permissions, grantResults)
    }
    finish() // Close the activity after handling the result
  }

  private fun sendPermissionResult(
    permissions: Array<out String>,
    grantResults: IntArray
  ) {
    val intent = Intent(ACTION_PERMISSIONS_RESULT)
    intent.putExtra(EXTRA_PERMISSIONS, permissions)
    intent.putExtra(EXTRA_PERMISSIONS_GRANTED, grantResults)
    LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
  }

  private fun hasOverlayPermission() {
    serviceClass?.let {
      val serviceIntent = Intent(this, serviceClass)
      startForegroundService(serviceIntent)
    }
  }
}