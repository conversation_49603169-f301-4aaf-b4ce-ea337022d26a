package cn.cleartv.terminal.ui.dialog

import android.content.Context
import android.text.InputType
import android.view.LayoutInflater
import android.widget.EditText
import androidx.appcompat.app.AlertDialog
import cn.cleartv.terminal.R

class TextInputDialog private constructor(
    private val context: Context,
    private val title: String,
    private val hint: String,
    private val initialValue: String?,
    private val inputType: Int,
    private val onConfirm: (String) -> Unit,
    private val onCancel: (() -> Unit)?
) {

    class Builder(private val context: Context) {
        private var title: String = ""
        private var hint: String = ""
        private var initialValue: String? = null
        private var inputType: Int = InputType.TYPE_CLASS_TEXT
        private var onConfirm: (String) -> Unit = {}
        private var onCancel: (() -> Unit)? = null

        fun setTitle(title: String) = apply { this.title = title }
        fun setHint(hint: String) = apply { this.hint = hint }
        fun setInitialValue(value: String?) = apply { this.initialValue = value }
        fun setInputType(inputType: Int) = apply { this.inputType = inputType }
        fun setOnConfirmListener(listener: (String) -> Unit) = apply { this.onConfirm = listener }
        fun setOnCancelListener(listener: () -> Unit) = apply { this.onCancel = listener }

        fun build(): AlertDialog {
            val view = LayoutInflater.from(context).inflate(R.layout.dialog_text_input, null)
            val editText = view.findViewById<EditText>(R.id.etTextInput)
            editText.hint = hint
            editText.inputType = inputType
            initialValue?.let { editText.setText(it) }

            val dialog = AlertDialog.Builder(context)
                .setTitle(title)
                .setView(view)
                .setPositiveButton(R.string.confirm) { _, _ ->
                    onConfirm(editText.text.toString().trim())
                }
                .setNegativeButton(R.string.cancel) { _, _ ->
                    onCancel?.invoke()
                }
                .setCancelable(true)
                .create()

            // 显示对话框时自动弹出键盘
            dialog.setOnShowListener {
                editText.requestFocus()
            }

            return dialog
        }

        fun show(): AlertDialog {
            val dialog = build()
            dialog.show()
            return dialog
        }
    }
}