package cn.cleartv.terminal

import android.content.Intent
import android.os.Build
import cn.cleartv.terminal.ClearTerminalService

class App : android.app.Application() {


  companion object {
    lateinit var instance: App
  }

  override fun onCreate() {
    super.onCreate()
    instance = this
    startClearService()
  }

  private fun startClearService() {
    // 启动服务
    val serviceIntent = Intent(this, ClearTerminalService::class.java)
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      startForegroundService(serviceIntent)
    } else {
      startService(serviceIntent)
    }
  }
}