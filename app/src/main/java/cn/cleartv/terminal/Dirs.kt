package cn.cleartv.terminal

import cn.cleartv.terminal.utils.FileUtils
import java.io.File
import kotlin.apply

object Dirs {
  val wwwDir: File by lazy {
    File(FileUtils.getInternalAppDataPath(), "www").apply {
      FileUtils.createOrExistsDir(this)
    }
  }

  val uploadDir: File by lazy {
    File(wwwDir, "upload").apply {
      FileUtils.createOrExistsDir(this)
    }
  }

  val frontendDir: File by lazy {
    File(wwwDir, "frontend").apply {
      FileUtils.createOrExistsDir(this)
    }
  }

  val tempDir: File by lazy {
    File(FileUtils.getInternalAppDataPath(), "temp").apply {
      FileUtils.createOrExistsDir(this)
    }
  }
  val logDir: File by lazy {
    File(FileUtils.getInternalAppDataPath(), "log").apply {
      FileUtils.createOrExistsDir(this)
    }
  }
  val logCacheDir: File by lazy {
    File(FileUtils.getInternalAppDataPath(), "log_cache").apply {
      FileUtils.createOrExistsDir(this)
    }
  }

  val pluginDir: File by lazy {
    File(FileUtils.getInternalAppDataPath(), "plugin").apply {
      FileUtils.createOrExistsDir(this)
    }
  }

  val downloadDir: File by lazy {
    File(FileUtils.getInternalAppDataPath(), "download").apply {
      FileUtils.createOrExistsDir(this)
    }
  }

}