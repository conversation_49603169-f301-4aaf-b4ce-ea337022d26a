package cn.cleartv.terminal

object Constants {

  object MapAppPackageName {
    const val BAIDU = "com.baidu.BaiduMap"
    const val GAODE = "com.autonavi.minimap"
    const val TENCENT = "com.tencent.map"
  }

  object RequestCode {
    const val REQUEST_AUDIO_PERMISSION = 1001
    const val REQUEST_CAMERA_PERMISSION = 1002
    const val REQUEST_MEDIA_PERMISSION = 1003
    const val REQUEST_FILE_PERMISSION = 1004
    const val REQUEST_CHOOSE_FILE = 2000
    const val REQUEST_LOCATION = 3000
  }


  object IntentKey {
  }

  object RegexString {
    const val URL = "^[a-zA-Z]+://" +
        "(?:" +
        "(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}" + // 域名
        "|" +
        "(?:(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)" + // IPv4
        "|" +
        "[a-zA-Z0-9-]+" + // 单段主机名（如localhost）
        ")" +
        "(?::\\d+)?" + // 端口
        "(?:/.*)?" + // 路径
        "$"
  }
}