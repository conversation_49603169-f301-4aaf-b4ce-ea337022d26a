package cn.cleartv.terminal.log

import android.os.Looper
import android.os.Process
import cn.cleartv.terminal.Dirs
import cn.cleartv.terminal.isDebug
import cn.cleartv.terminal.toJson
import com.tencent.mars.xlog.Xlog
import java.io.File


object Log {

  init {
    System.loadLibrary("c++_shared")
    System.loadLibrary("marsxlog")
  }

  const val DEFAULT_TAG: String = "ClearTerminal"
  const val NAME_PREFIX: String = "ClearTerminal"
  val logPath: File = Dirs.logDir
  val logCachePath: File = Dirs.logCacheDir

  private val xlog: Xlog by lazy {
    Xlog().apply {
      val logConfig: Xlog.XLogConfig = Xlog.XLogConfig()
      logConfig.mode = Xlog.AppednerModeAsync
      logConfig.logdir = logPath.absolutePath
      logConfig.nameprefix = NAME_PREFIX
      logConfig.pubkey = ""
      logConfig.compressmode = Xlog.ZLIB_MODE
      logConfig.compresslevel = 0
      logConfig.cachedir = logCachePath.absolutePath
      logConfig.cachedays = 0
      if (isDebug) {
        logConfig.level = Xlog.LEVEL_VERBOSE
        setConsoleLogOpen(0L, true)
      } else {
        logConfig.level = Xlog.LEVEL_INFO
        setConsoleLogOpen(0L, false)
      }
      appenderOpen(
        logConfig.level,
        logConfig.mode,
        logConfig.cachedir,
        logConfig.logdir,
        logConfig.nameprefix,
        logConfig.cachedays
      )
    }
  }

  fun flush() {
    xlog.appenderFlush(0L, true)
  }

  private fun Array<*>.toFormatString(): String {
    return this.joinToString("; ") {
      when (it) {
        is String -> it
        is Throwable -> it.stackTraceToString()
        else -> it?.toJson()?: "null"
      }
    }
  }

  private fun getCallerStackTraceElement(): StackTraceElement? {
    val stackTrace = Throwable().stackTrace
    for (i in stackTrace.indices) {
      val element = stackTrace[i]
      val className = element.className
      if (className.startsWith("cn.cleartv.terminal.log.Log")) {
        continue // Skip frames from this Log class
      }
      if (className.startsWith("kotlinx.coroutines.")) { // Common method for coroutine state machines
        continue
      }
      return element
    }
    return null // Fallback
  }

  fun v(vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_VERBOSE) {
      val element: StackTraceElement? = getCallerStackTraceElement()
      xlog.logV(
        0L,
        element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

  fun d(vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_DEBUG) {
      val element: StackTraceElement? = getCallerStackTraceElement()
      xlog.logD(
        0L,
        element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

  fun i(vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_INFO) {
      val element: StackTraceElement? = getCallerStackTraceElement()
      xlog.logI(
        0L,
        element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

  fun w(vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_WARNING) {
      val element: StackTraceElement? = getCallerStackTraceElement()
      xlog.logW(
        0L,
        element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

  fun e(vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_ERROR) {
      val element: StackTraceElement? = getCallerStackTraceElement()
      xlog.logE(
        0L,
        element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

  fun f(vararg msg: Any?) {
    if (xlog.getLogLevel(0L) <= Xlog.LEVEL_FATAL) {
      val element: StackTraceElement? = getCallerStackTraceElement()
      xlog.logF(
        0L,
        element?.fileName?.substringBefore('.') ?: DEFAULT_TAG,
        element?.fileName ?: "",
        element?.methodName ?: "",
        element?.lineNumber ?: 0,
        Process.myPid(),
        Thread.currentThread().id,
        Looper.getMainLooper().thread.id,
        msg.toFormatString()
      )
    }
  }

}
