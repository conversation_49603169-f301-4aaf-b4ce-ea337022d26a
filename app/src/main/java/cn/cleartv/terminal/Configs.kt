package cn.cleartv.terminal

import androidx.annotation.StringDef
import cn.cleartv.terminal.utils.DataStorePreferenceAdapter
import cn.cleartv.terminal.utils.DataStoreUtils
import com.google.gson.annotations.SerializedName

val configDataStore by lazy { DataStoreUtils.getDataStore("configs") }
val configPreferenceAdapter by lazy { DataStorePreferenceAdapter(configDataStore) }

val configs: Configs get() = Configs()

data class Configs(
  @SerializedName(SystemConfig.SYSTEM_CONFIG_KEY)
  val systemConfig: Map<String, Any?>? = mapOf(
    SystemConfig.ENABLE_ADB to SystemConfig.enableAdb,
    SystemConfig.ENABLE_ASSISTANT to SystemConfig.enableAssistant
  ),
  @SerializedName(TerminalConfig.TERMINAL_CONFIG_KEY)
  val terminalConfig: Map<String, Any?>? = mapOf(
    TerminalConfig.TERMINAL_NAME_KEY to TerminalConfig.name,
    TerminalConfig.TERMINAL_TYPE_KEY to TerminalConfig.type,
    TerminalConfig.TERMINAL_DESCRIPTION_KEY to TerminalConfig.description,
    TerminalConfig.TERMINAL_REMARK_KEY to TerminalConfig.remark,
    TerminalConfig.TERMINAL_LOCATION_KEY to TerminalConfig.location,
    TerminalConfig.TERMINAL_EXTRA_INFO_KEY to TerminalConfig.extraInfo
  ),
  @SerializedName(LauncherConfig.LAUNCHER_CONFIG_KEY)
  val launcherConfig: Map<String, Any?>? = mapOf(
    LauncherConfig.LAUNCHER_TYPE_KEY to LauncherConfig.type,
    LauncherConfig.LAUNCHER_PATH_KEY to LauncherConfig.path,
    LauncherConfig.LAUNCHER_PARAMS_KEY to LauncherConfig.params
  ),
  @SerializedName(ServerConfig.SERVER_CONFIG_KEY)
  val serverConfig: Map<String, Any?>? = mapOf(
    ServerConfig.SERVER_HOST_KEY to ServerConfig.hostUrl
  )
) {

  /**
   * 保存配置到本地
   */
  fun saveToLocal() {
    // 系统配置
    systemConfig?.let {
      (systemConfig[SystemConfig.ENABLE_ADB] as? Boolean)?.let { value -> SystemConfig.enableAdb = value }
      (systemConfig[SystemConfig.ENABLE_ASSISTANT] as? Boolean)?.let { value -> SystemConfig.enableAssistant = value }
    }

    // 终端配置
    terminalConfig?.let {
      (terminalConfig[TerminalConfig.TERMINAL_NAME_KEY] as? String)?.let { value -> TerminalConfig.name = value }
      (terminalConfig[TerminalConfig.TERMINAL_TYPE_KEY] as? String)?.let { value -> TerminalConfig.type = value }
      (terminalConfig[TerminalConfig.TERMINAL_DESCRIPTION_KEY] as? String)?.let { value -> TerminalConfig.description = value }
      (terminalConfig[TerminalConfig.TERMINAL_REMARK_KEY] as? String)?.let { value -> TerminalConfig.remark = value }
      (terminalConfig[TerminalConfig.TERMINAL_LOCATION_KEY] as? String)?.let { value -> TerminalConfig.location = value }
      (terminalConfig[TerminalConfig.TERMINAL_EXTRA_INFO_KEY] as? String)?.let { value -> TerminalConfig.extraInfo = value }
    }

    // 服务器配置
    serverConfig?.let {
      (serverConfig[ServerConfig.SERVER_HOST_KEY] as? String)?.let { value -> ServerConfig.hostUrl = value }
    }

    // 启动器配置
    launcherConfig?.let {
      (launcherConfig[LauncherConfig.LAUNCHER_TYPE_KEY] as? String)?.let { value -> LauncherConfig.type = value }
      (launcherConfig[LauncherConfig.LAUNCHER_PATH_KEY] as? String)?.let { value -> LauncherConfig.path = value }
      (launcherConfig[LauncherConfig.LAUNCHER_PARAMS_KEY] as? String)?.let { value -> LauncherConfig.params = value }
    }
  }


}

object SystemConfig {
  const val SYSTEM_CONFIG_KEY = "system_config"
  const val ENABLE_ADB = "enable_adb"
  const val ENABLE_ASSISTANT = "enable_assistant"

  var enableAdb: Boolean
    get() = configDataStore.getSyncData(ENABLE_ADB, true)
    set(value) = configDataStore.putSyncData(ENABLE_ADB, value)

  var enableAssistant: Boolean
    get() = configDataStore.getSyncData(ENABLE_ASSISTANT, true)
    set(value) = configDataStore.putSyncData(ENABLE_ASSISTANT, value)
}

object TerminalConfig {

  const val TERMINAL_CONFIG_KEY = "terminal_config"
  const val TERMINAL_NAME_KEY = "terminal_name"
  const val TERMINAL_TYPE_KEY = "terminal_type"
  const val TERMINAL_DESCRIPTION_KEY = "terminal_description"
  const val TERMINAL_REMARK_KEY = "terminal_remark"
  const val TERMINAL_LOCATION_KEY = "terminal_location"
  const val TERMINAL_EXTRA_INFO_KEY = "terminal_extra_info"

  var name: String
    get() = configDataStore.getSyncData(TERMINAL_NAME_KEY, "ClearTerminal")
    set(value) = configDataStore.putSyncData(TERMINAL_NAME_KEY, value)

  var type: String
    get() = configDataStore.getSyncData(TERMINAL_TYPE_KEY, "Default")
    set(value) = configDataStore.putSyncData(TERMINAL_TYPE_KEY, value)

  var description: String?
    get() = configDataStore.getSyncData(TERMINAL_DESCRIPTION_KEY, "")
    set(value) = configDataStore.putSyncData(TERMINAL_DESCRIPTION_KEY, value)

  var remark: String?
    get() = configDataStore.getSyncData(TERMINAL_REMARK_KEY, "")
    set(value) = configDataStore.putSyncData(TERMINAL_REMARK_KEY, value)

  var location: String?
    get() = configDataStore.getSyncData(TERMINAL_LOCATION_KEY, "")
    set(value) = configDataStore.putSyncData(TERMINAL_LOCATION_KEY, value)

  var extraInfo: String?
    get() = configDataStore.getSyncData(TERMINAL_EXTRA_INFO_KEY, "")
    set(value) = configDataStore.putSyncData(TERMINAL_EXTRA_INFO_KEY, value)

}

object ServerConfig {
  const val SERVER_CONFIG_KEY = "server_config"
  const val SERVER_HOST_KEY = "server_host_url"

  var hostUrl: String
    get() = configDataStore.getSyncData(SERVER_HOST_KEY, "http://**************:8081")
    set(value) = configDataStore.putSyncData(SERVER_HOST_KEY, value)
}

object LauncherConfig {
  const val LAUNCHER_CONFIG_KEY = "launcher_config"
  const val LAUNCHER_TYPE_KEY = "launcher_type"
  const val LAUNCHER_PATH_KEY = "launcher_path"
  const val LAUNCHER_PARAMS_KEY = "launcher_params"

  const val LAUNCHER_TYPE_DEFAULT = "DEFAULT"
  const val LAUNCHER_TYPE_APP = "APP"
  const val LAUNCHER_TYPE_WEB = "WEB"
  const val LAUNCHER_TYPE_FILE = "FILE"

  @Retention(AnnotationRetention.RUNTIME)
  @StringDef(LAUNCHER_TYPE_DEFAULT, LAUNCHER_TYPE_APP, LAUNCHER_TYPE_WEB, LAUNCHER_TYPE_FILE)
  @Target(AnnotationTarget.PROPERTY)
  @MustBeDocumented
  annotation class LauncherType

  @LauncherType
  var type: String
    get() = configDataStore.getSyncData(LAUNCHER_TYPE_KEY, LAUNCHER_TYPE_DEFAULT)
    set(value) = configDataStore.putSyncData(LAUNCHER_TYPE_KEY, value)
  var path: String?
    get() = configDataStore.getSyncData(LAUNCHER_PATH_KEY, "")
    set(value) = configDataStore.putSyncData(LAUNCHER_PATH_KEY, value)
  var params: String?
    get() = configDataStore.getSyncData(LAUNCHER_PARAMS_KEY, "")
    set(value) = configDataStore.putSyncData(LAUNCHER_PARAMS_KEY, value)

}
