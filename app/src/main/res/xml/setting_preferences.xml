<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <PreferenceCategory
        app:key="launcherCategory"
        app:title="启动设置">

        <ListPreference
            app:key="launcher_type"
            app:title="启动类型"
            android:defaultValue="DEFAULT"
            app:entryValues="@array/launcher_type_values"
            app:entries="@array/launcher_type_names"
            app:useSimpleSummaryProvider="true"/>

        <EditTextPreference
            app:key="launcher_path"
            app:title="启动路径"
            app:useSimpleSummaryProvider="true"/>

        <EditTextPreference
            app:key="launcher_params"
            app:title="启动参数"
            app:useSimpleSummaryProvider="true"/>

        <Preference
            app:key="relaunch"
            app:title="重新启动" />

    </PreferenceCategory>

    <PreferenceCategory
        app:title="设备信息">

        <Preference
            app:key="versionName"
            app:summary="1.0"
            app:title="版本号"
            app:useSimpleSummaryProvider="true" />

        <Preference
            app:key="systemSetting"
            app:title="系统设置" />

        <Preference
            app:key="ip"
            app:title="IP" />

        <Preference
            app:key="romInfo"
            app:title="ROM" />

        <Preference
            app:key="reboot"
            app:title="重启设备" />

    </PreferenceCategory>

</PreferenceScreen>