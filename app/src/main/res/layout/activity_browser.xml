<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.BrowserActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:background="@color/design_default_color_primary"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/logo_icon"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginStart="12dp"
            android:scaleType="centerInside"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"

            app:layout_constraintTop_toTopOf="parent"
            tools:src="@mipmap/ic_launcher_round"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textColor="@color/white"
            android:textSize="24sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/logo_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/menu_icon"
            tools:text="@string/app_name" />

        <EditText
            android:id="@+id/url"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:maxLines="1"
            android:singleLine="true"
            android:inputType="textUri"
            android:imeOptions="actionGo"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/logo_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/menu_icon"
            android:visibility="gone"/>


        <ImageView
            android:id="@+id/menu_icon"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:padding="4dp"
            android:layout_marginEnd="12dp"
            android:scaleType="centerInside"
            app:tint="@color/white"
            android:src="@android:drawable/ic_menu_info_details"

            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_bar">

        <FrameLayout
            android:id="@+id/web_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:max="100"

        app:layout_constraintTop_toBottomOf="@id/title_bar" />

</androidx.constraintlayout.widget.ConstraintLayout>