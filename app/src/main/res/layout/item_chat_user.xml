<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/messageContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="48dp"
        android:background="@drawable/user_message_background"
        android:elevation="2dp"
        android:padding="12dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintWidth_max="280dp"
        app:layout_constraintHorizontal_bias="1.0">

        <TextView
            android:id="@+id/messageText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="用户消息内容"
            android:textSize="16sp"
            android:textColor="?attr/colorOnPrimary"
            android:lineSpacingExtra="2dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <ImageButton
            android:id="@+id/voicePlayButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/voice_play_button_background"
            android:src="@drawable/ic_play_arrow"
            android:contentDescription="播放语音"
            android:tint="?attr/colorOnPrimary"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/timeText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="12:34"
            android:textSize="12sp"
            android:textColor="?attr/colorOnPrimary"
            android:alpha="0.7"
            app:layout_constraintTop_toBottomOf="@id/messageText"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>