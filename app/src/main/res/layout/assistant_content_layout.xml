<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintHeight_percent="0.382"
        android:padding="12dp"
        android:background="@drawable/fade_to_top_gradient"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 聊天消息列表 -->
        <cn.cleartv.terminal.widget.FadingEdgeRecycleView
            android:id="@+id/chatRecyclerView"
            android:requiresFadingEdge="vertical"
            android:fadingEdgeLength="24dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:scrollbarStyle="outsideOverlay"
            android:scrollbars="vertical"
            android:fadeScrollbars="true"
            tools:listitem="@layout/item_chat_ai"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
