<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/ivFunctionIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginBottom="8dp"
            android:src="@drawable/ic_web"
            android:contentDescription="@null" />

        <TextView
            android:id="@+id/tvFunctionName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/black" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
