<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>

    <application
        android:name="cn.cleartv.terminal.App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Terminal"
        android:usesCleartextTraffic="true"
        tools:ignore="UnusedAttribute">

        <activity
            android:name="cn.cleartv.terminal.ui.SplashActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name="cn.cleartv.terminal.ui.BrowserActivity"
            android:theme="@style/FullscreenStyle"
            android:exported="true" />

        <activity
            android:name="cn.cleartv.terminal.ui.FileViewActivity"
            android:theme="@style/FullscreenStyle"
            android:exported="true" />

        <activity
            android:name="cn.cleartv.terminal.ui.HomeActivity"
            android:theme="@style/FullscreenStyle"
            android:exported="true" />

        <activity
            android:name="cn.cleartv.terminal.ui.SettingActivity"
            android:exported="true" />

        <service
            android:name="cn.cleartv.terminal.ClearTerminalService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="dataSync"
            tools:ignore="ExportedService" />

        <service android:name="cn.cleartv.terminal.assistant.AssistantService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="microphone"/>

        <activity android:name="cn.cleartv.terminal.assistant.AiAssistantActivity"
            android:theme="@style/TransparentStyle"/>

        <activity android:name="cn.cleartv.terminal.ui.PermissionRequestActivity"
            android:theme="@style/TransparentStyle"
            android:exported="false"/>

    </application>

</manifest>